//! Player Entity Component System
//! 
//! Extracted from the lighting diorama prototype, this module provides
//! a Player entity with sprite rendering, normal mapping, and lighting integration.

use bevy::{
    prelude::*,
    pbr::{ExtendedMaterial, MaterialExtension},
    render::render_resource::{AsBindGroup, ShaderRef},
};
use crate::components::particle_system::EngineExhaust;

/// Player component following game industry standards
/// Represents the main player entity with movement and rendering capabilities
#[derive(Component, Clone)]
pub struct Player {
    /// Movement speed in units per second
    pub speed: f32,
    /// Rotation speed in radians per second
    pub rotation_speed: f32,
    /// Player health points
    pub health: f32,
    /// Maximum health
    pub max_health: f32,
    /// Whether the player can move
    pub can_move: bool,
}

impl Default for Player {
    fn default() -> Self {
        Self {
            speed: 5.0,
            rotation_speed: 2.0,
            health: 100.0,
            max_health: 100.0,
            can_move: true,
        }
    }
}

/// Marker component for player sprite rendering
#[derive(Component)]
pub struct PlayerSprite;

/// Extension to StandardMaterial that adds time-based effects for player sprite
#[derive(Asset, AsBindGroup, TypeP<PERSON>, Debug, Clone)]
pub struct PlayerMaterialExtension {
    /// Time uniform for animations
    #[uniform(100)]
    pub time: f32,
}

impl MaterialExtension for PlayerMaterialExtension {
    fn fragment_shader() -> ShaderRef {
        "shaders/extended_material.wgsl".into()
    }
}

/// Type alias for our extended player material
pub type PlayerMaterial = ExtendedMaterial<StandardMaterial, PlayerMaterialExtension>;

/// Configuration for player sprite appearance
#[derive(Resource, Clone)]
pub struct PlayerSpriteConfig {
    /// Size of the player sprite quad
    pub sprite_size: Vec2,
    /// Path to the base texture
    pub texture_path: String,
    /// Path to the normal map texture
    pub normal_map_path: String,
    /// Material properties
    pub metallic: f32,
    pub perceptual_roughness: f32,
    pub reflectance: f32,
}

impl Default for PlayerSpriteConfig {
    fn default() -> Self {
        Self {
            sprite_size: Vec2::new(2.0, 2.0), // Smaller sprite for better 2D view
            texture_path: "textures/ship.png".to_string(),
            normal_map_path: "textures/ship_normal.png".to_string(),
            metallic: 0.75,
            perceptual_roughness: 0.3,
            reflectance: 0.5,
        }
    }
}

/// Plugin for the Player system
pub struct PlayerPlugin;

impl Plugin for PlayerPlugin {
    fn build(&self, app: &mut App) {
        app.add_plugins(MaterialPlugin::<PlayerMaterial>::default())
            .init_resource::<PlayerSpriteConfig>()
            .add_systems(
                Update,
                (
                    spawn_player_sprite_components,
                    update_player_material_time,
                    rotate_player_sprite,
                    handle_player_input,
                ),
            );
    }
}

/// System to spawn the visual components for Player entities
pub fn spawn_player_sprite_components(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<PlayerMaterial>>,
    asset_server: Res<AssetServer>,
    config: Res<PlayerSpriteConfig>,
    player_query: Query<(Entity, &Player, &Transform), (Added<Player>, Without<PlayerSprite>)>,
) {
    for (entity, _player, _transform) in player_query.iter() {
        // Load textures
        let ship_texture = asset_server.load(&config.texture_path);
        let ship_normal_map = asset_server.load(&config.normal_map_path);

        // Create quad mesh
        let quad_mesh = meshes.add(Rectangle::new(config.sprite_size.x, config.sprite_size.y));

        // Create extended material with normal mapping
        let extended_material = materials.add(ExtendedMaterial {
            base: StandardMaterial {
                base_color_texture: Some(ship_texture),
                normal_map_texture: Some(ship_normal_map),
                metallic: config.metallic,
                perceptual_roughness: config.perceptual_roughness,
                reflectance: config.reflectance,
                emissive: LinearRgba::rgb(0.0, 0.0, 0.0),
                base_color: Color::srgb(0.0, 1.0, 0.0), // Bright green for debugging
                alpha_mode: AlphaMode::Opaque, // Changed from Blend to Opaque
                ..default()
            },
            extension: PlayerMaterialExtension {
                time: 0.0,
            },
        });

        // Add sprite components to the player entity
        commands.entity(entity).insert((
            Mesh3d(quad_mesh),
            MeshMaterial3d(extended_material),
            PlayerSprite,
        ));
    }
}

/// System to update the time uniform in player materials
pub fn update_player_material_time(
    time: Res<Time>,
    mut materials: ResMut<Assets<PlayerMaterial>>,
) {
    for (_, material) in materials.iter_mut() {
        material.extension.time = time.elapsed_secs();
    }
}

/// System to rotate the player sprite for demonstration
pub fn rotate_player_sprite(
    time: Res<Time>,
    mut query: Query<&mut Transform, (With<PlayerSprite>, With<Player>)>,
) {
    for mut transform in query.iter_mut() {
        transform.rotation *= Quat::from_rotation_z(time.delta_secs() * 0.2);
    }
}

/// System to handle basic player input
pub fn handle_player_input(
    keyboard_input: Res<ButtonInput<KeyCode>>,
    time: Res<Time>,
    mut player_query: Query<(&mut Transform, &Player), (With<PlayerSprite>, Without<Camera>)>,
    _exhaust_query: Query<&mut EngineExhaust>,
) {
    for (mut transform, player) in player_query.iter_mut() {
        if !player.can_move {
            continue;
        }

        let mut movement = Vec3::ZERO;
        let mut rotation = 0.0;

        // Movement input
        if keyboard_input.pressed(KeyCode::KeyW) || keyboard_input.pressed(KeyCode::ArrowUp) {
            movement.y += 1.0;
        }
        if keyboard_input.pressed(KeyCode::KeyS) || keyboard_input.pressed(KeyCode::ArrowDown) {
            movement.y -= 1.0;
        }
        if keyboard_input.pressed(KeyCode::KeyA) || keyboard_input.pressed(KeyCode::ArrowLeft) {
            movement.x -= 1.0;
        }
        if keyboard_input.pressed(KeyCode::KeyD) || keyboard_input.pressed(KeyCode::ArrowRight) {
            movement.x += 1.0;
        }

        // Rotation input
        if keyboard_input.pressed(KeyCode::KeyQ) {
            rotation += 1.0;
        }
        if keyboard_input.pressed(KeyCode::KeyE) {
            rotation -= 1.0;
        }

        // Apply movement
        if movement.length() > 0.0 {
            movement = movement.normalize();
            transform.translation += movement * player.speed * time.delta_secs();
        }

        // Apply rotation
        if rotation != 0.0 {
            transform.rotation *= Quat::from_rotation_z(rotation * player.rotation_speed * time.delta_secs());
        }

        // Update engine exhaust based on movement will be handled separately
        // for now to avoid borrow checker issues
    }
}

/// Helper function to create a Player entity with default configuration
pub fn create_player(position: Vec3) -> impl Bundle {
    (
        Player::default(),
        Transform::from_translation(position)
            .with_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)), // Rotate to face up for top-down view
        Visibility::default(),
    )
}
