#import bevy_pbr::{
    pbr_fragment::pbr_input_from_standard_material,
    pbr_functions::{apply_pbr_lighting, main_pass_post_lighting_processing},
    forward_io::{VertexOutput, FragmentOutput},
}

// Our custom extension bindings
@group(2) @binding(100)
var<uniform> time: f32;

@fragment
fn fragment(
    in: VertexOutput,
    @builtin(front_facing) is_front: bool,
) -> FragmentOutput {
    // Get the standard PBR input which includes all lighting information
    // This automatically handles the normal map texture and base color texture
    var pbr_input = pbr_input_from_standard_material(in, is_front);

    // Simple time-based metallic variation to show dynamic effects
    let metallic_pulse = sin(time * 1.5) * 0.1 + 0.9;
    pbr_input.material.metallic *= metallic_pulse;

    // Apply PBR lighting - this uses all PointLights in the scene
    // and properly applies normal mapping for surface detail
    var out: FragmentOutput;
    out.color = apply_pbr_lighting(pbr_input);

    // Apply standard post-processing
    out.color = main_pass_post_lighting_processing(pbr_input, out.color);

    return out;
}
