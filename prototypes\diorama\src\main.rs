
use bevy::{
    prelude::*,
    pbr::{ExtendedMaterial, MaterialExtension},
    render::render_resource::{AsBindGroup, ShaderRef},
    state::state::States,
    diagnostic::{DiagnosticsStore, FrameTimeDiagnosticsPlugin},
};
use rand;

mod thruster_system;
mod point_light_orb;
use thruster_system::*;
use point_light_orb::*;

fn main() {
    App::new()
        .add_plugins((
            DefaultPlugins,
            MaterialPlugin::<MyMaterial>::default(),
            MaterialPlugin::<ThrusterMaterial>::default(),
            FrameTimeDiagnosticsPlugin::default(),
            PointLightOrbPlugin,
        ))
        .insert_resource(AmbientLight {
            color: Color::srgb(0.1, 0.1, 0.2),
            brightness: 0.02, // Even lower ambient for stress test visibility
            affects_lightmapped_meshes: false,
        })
        .init_state::<DemoMode>()
        .insert_resource(ExplosionTimer {
            timer: 0.0,
            next_explosion_time: 1.0,
        })
        .insert_resource(FpsStats::default())
        .insert_resource(StressTestConfig::default())
        .add_systems(Startup, setup)
        .add_systems(Update, (
            handle_input,
            handle_stress_test_controls,
            rotate_sprite,
            update_ui_text,
            update_material_time,
            update_fps_display,
            update_fps_stats,
        ))
        .add_systems(Update, handle_explosions.run_if(in_state(DemoMode::Explosions)))
        .add_systems(Update, (update_thrusters, control_thruster_power).run_if(in_state(DemoMode::Thrusters)))
        .add_systems(OnEnter(DemoMode::Default), enter_default_mode)
        .add_systems(OnExit(DemoMode::Default), exit_default_mode)
        .add_systems(OnEnter(DemoMode::Explosions), enter_explosion_mode)
        .add_systems(OnExit(DemoMode::Explosions), exit_explosion_mode)
        .add_systems(OnEnter(DemoMode::Orbital), enter_orbital_mode)
        .add_systems(OnExit(DemoMode::Orbital), exit_orbital_mode)
        .add_systems(OnEnter(DemoMode::StressTest), enter_stress_test_mode)
        .add_systems(OnExit(DemoMode::StressTest), exit_stress_test_mode)
        .add_systems(OnEnter(DemoMode::Thrusters), spawn_thrusters)
        .add_systems(OnExit(DemoMode::Thrusters), cleanup_thrusters)
        .add_systems(OnEnter(DemoMode::PointLightOrbs), spawn_point_light_orb_demo)
        .add_systems(OnExit(DemoMode::PointLightOrbs), cleanup_point_light_orb_demo)
        .run();
}

/// A marker component for our sprite quad so we can query for it specifically.
#[derive(Component)]
struct SpriteQuad;

/// Component for lights that move around and change brightness
#[derive(Component)]
struct MovingLight {
    radius: f32,
    speed: f32,
    height_variation: f32,
    brightness_variation: f32,
    base_intensity: f32,
}

/// Demo modes for different lighting scenarios
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, States, Default)]
enum DemoMode {
    #[default]
    Default,    // Moving colored lights
    Explosions, // Pitch black with random explosions
    Orbital,    // Lights orbiting on all 3 axis planes
    StressTest, // Hundreds of lights for performance testing
    Thrusters,  // Shader-based thrusters with dynamic lighting
    PointLightOrbs, // Test the new PointLightOrb entities
}

/// Resource to track explosion timing
#[derive(Resource)]
struct ExplosionTimer {
    timer: f32,
    next_explosion_time: f32,
}

/// Component for explosion lights
#[derive(Component)]
struct ExplosionLight {
    lifetime: f32,
    max_lifetime: f32,
    max_intensity: f32,
}

/// Component for orbital lights that move in circles on different planes
#[derive(Component)]
struct OrbitalLight {
    radius: f32,
    speed: f32,
    axis_plane: u32, // 0=XY, 1=XZ, 2=YZ
    phase_offset: f32,
    base_intensity: f32,
    color_shift_speed: f32,
}

/// Component for stress test lights
#[derive(Component)]
struct StressTestLight {
    base_position: Vec3,
    movement_radius: f32,
    movement_speed: f32,
    color_cycle_speed: f32,
    intensity_variation: f32,
}

/// Component for the FPS display text
#[derive(Component)]
struct FpsText;

/// Component to mark entities that represent visible light orbs
#[derive(Component)]
struct LightOrb;

/// Resource to track FPS statistics
#[derive(Resource)]
struct FpsStats {
    current_fps: f64,
    min_fps: f64,
    max_fps: f64,
    avg_fps: f64,
    frame_count: u64,
    fps_sum: f64,
    last_reset_time: f64,
}

impl Default for FpsStats {
    fn default() -> Self {
        Self {
            current_fps: 0.0,
            min_fps: f64::INFINITY,
            max_fps: 0.0,
            avg_fps: 0.0,
            frame_count: 0,
            fps_sum: 0.0,
            last_reset_time: 0.0,
        }
    }
}

/// Resource to track stress test light count
#[derive(Resource)]
struct StressTestConfig {
    light_count: u32,
    max_lights_spawned: u32,
}

impl Default for StressTestConfig {
    fn default() -> Self {
        Self {
            light_count: 200,
            max_lights_spawned: 0,
        }
    }
}

/// Extension to StandardMaterial that adds time-based effects
#[derive(Asset, AsBindGroup, TypePath, Debug, Clone)]
pub struct MyExtension {
    /// Time uniform for animations
    #[uniform(100)]
    time: f32,
}

impl MaterialExtension for MyExtension {
    fn fragment_shader() -> ShaderRef {
        "shaders/extended_material.wgsl".into()
    }
}

/// Type alias for our extended material
type MyMaterial = ExtendedMaterial<StandardMaterial, MyExtension>;

/// This system runs once at startup to set up the scene.
fn setup(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<MyMaterial>>,
    asset_server: Res<AssetServer>,
) {
    // Load our textures
    let ship_texture = asset_server.load("ship.png");
    let ship_normal_map = asset_server.load("ship_normal.png");

    // Create a new quad mesh. We can control the size here.
    let quad_mesh = meshes.add(Rectangle::new(4.0, 4.0));

    // Create an extended material that combines StandardMaterial with our custom extension
    let extended_material = materials.add(ExtendedMaterial {
        base: StandardMaterial {
            base_color_texture: Some(ship_texture),
            normal_map_texture: Some(ship_normal_map),
            // Good reflections without being excessive
            metallic: 0.75,
            // Balanced surface properties for clear normal mapping
            perceptual_roughness: 0.3,
            // Standard reflectance
            reflectance: 0.5,
            // No emissive for cleaner lighting demonstration
            emissive: LinearRgba::rgb(0.0, 0.0, 0.0),
            // Standard base color
            base_color: Color::WHITE,
            alpha_mode: AlphaMode::Blend,
            ..default()
        },
        extension: MyExtension {
            time: 0.0,
        },
    });

    // Spawn the sprite quad
    commands.spawn((
        Mesh3d(quad_mesh),
        MeshMaterial3d(extended_material),
        Transform::from_xyz(0.0, 0.0, 0.0),
        SpriteQuad, // Add our marker component
    ));



    // Spawn PointLightOrbs for default moving lights demo
    // Orange orb - circular orbit
    commands.spawn(create_moving_point_light_orb(
        Color::srgb(1.0, 0.6, 0.1),
        1200.0,
        0.12,
        Vec3::new(4.0, 2.0, 4.0),
        true,
        MovementPattern::Orbit {
            center: Vec3::new(0.0, 2.0, 0.0),
            radius: 4.0,
            speed: 1.0,
        },
    ));

    // Blue orb - figure-8 pattern
    commands.spawn(create_moving_point_light_orb(
        Color::srgb(0.1, 0.4, 1.0),
        1000.0,
        0.11,
        Vec3::new(-4.0, -2.0, -4.0),
        true,
        MovementPattern::Figure8 {
            center: Vec3::new(0.0, 1.0, 0.0),
            scale: 3.0,
            speed: 0.8,
        },
    ));

    // Green orb - floating movement
    commands.spawn(create_moving_point_light_orb(
        Color::srgb(0.2, 1.0, 0.3),
        1100.0,
        0.10,
        Vec3::new(0.0, 4.0, 2.0),
        true,
        MovementPattern::Float {
            center: Vec3::new(0.0, 3.0, 1.0),
            range: 2.0,
            speed: 1.5,
        },
    ));

    // Spawn the camera
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(0.0, 0.0, 8.0).looking_at(Vec3::ZERO, Vec3::Y),
    ));

    // Add UI text for controls
    commands.spawn((
        Text::new("Demo Controls:\n1 - Default Mode (Moving Lights)\n2 - Explosion Mode\n3 - Orbital Mode (3D Circles)\n4 - Stress Test (Dynamic Lights)\n5 - Thruster System (Shaders + Lights)\n6 - PointLightOrb Test\n\nStress Test Controls (Mode 4 only):\n+ (Plus) - Add 50 lights\n- (Minus) - Remove 50 lights\nR - Reset stats\n\nThruster Controls (Mode 5 only):\nSpace - Increase power\nShift - Decrease power\n\nFPS Monitor: Top-right corner\nGreen = Good (>45 FPS)\nYellow = OK (30-45 FPS)\nRed = Poor (<30 FPS)\n\nCurrent: Default Mode"),
        Node {
            position_type: PositionType::Absolute,
            top: Val::Px(10.0),
            left: Val::Px(10.0),
            ..default()
        },
        TextColor(Color::WHITE),
    ));

    // Add comprehensive FPS display in top-right corner
    commands.spawn((
        Text::new("FPS: --\nMin: --\nMax: --\nAvg: --\nLights: 0"),
        Node {
            position_type: PositionType::Absolute,
            top: Val::Px(10.0),
            right: Val::Px(10.0),
            ..default()
        },
        TextColor(Color::srgb(0.0, 1.0, 0.0)), // Start with green
        FpsText,
    ));
}


/// This system animates the moving lights to showcase normal mapping
fn animate_lights(
    time: Res<Time>,
    mut query: Query<(&mut Transform, &mut PointLight, &MovingLight)>,
) {
    for (mut transform, mut light, moving_light) in query.iter_mut() {
        let elapsed = time.elapsed_secs();

        // Calculate circular motion
        let angle = elapsed * moving_light.speed;
        let x = angle.cos() * moving_light.radius;
        let y = angle.sin() * moving_light.radius;

        // Add height variation
        let height_offset = (elapsed * moving_light.speed * 2.0).sin() * moving_light.height_variation;

        // Update position
        transform.translation.x = x;
        transform.translation.y = y;
        transform.translation.z += height_offset * time.delta_secs();

        // Vary brightness
        let brightness_factor = (elapsed * moving_light.speed * 1.5).sin() * 0.5 + 0.5;
        light.intensity = moving_light.base_intensity +
            (brightness_factor * moving_light.brightness_variation);
    }
}

/// Animate orbital lights moving in circles on different axis planes
fn animate_orbital_lights(
    time: Res<Time>,
    mut query: Query<(&mut Transform, &mut PointLight, &OrbitalLight)>,
) {
    for (mut transform, mut light, orbital) in query.iter_mut() {
        let elapsed = time.elapsed_secs();
        let angle = elapsed * orbital.speed + orbital.phase_offset;

        // Position based on axis plane
        match orbital.axis_plane {
            0 => { // XY plane
                transform.translation.x = angle.cos() * orbital.radius;
                transform.translation.y = angle.sin() * orbital.radius;
                transform.translation.z = 0.0;
            },
            1 => { // XZ plane
                transform.translation.x = angle.cos() * orbital.radius;
                transform.translation.y = 0.0;
                transform.translation.z = angle.sin() * orbital.radius;
            },
            2 => { // YZ plane
                transform.translation.x = 0.0;
                transform.translation.y = angle.cos() * orbital.radius;
                transform.translation.z = angle.sin() * orbital.radius;
            },
            _ => {} // Invalid plane
        }

        // Dynamic color shifting
        let color_time = elapsed * orbital.color_shift_speed;
        light.color = Color::srgb(
            0.5 + (color_time).sin() * 0.5,
            0.5 + (color_time + 2.0).sin() * 0.5,
            0.5 + (color_time + 4.0).sin() * 0.5,
        );

        // Dynamic intensity
        let intensity_factor = (elapsed * 2.0 + orbital.phase_offset).sin() * 0.3 + 0.7;
        light.intensity = orbital.base_intensity * intensity_factor;
    }
}

/// Animate stress test lights with chaotic movement and colors
fn animate_stress_test_lights(
    time: Res<Time>,
    mut query: Query<(&mut Transform, &mut PointLight, &StressTestLight)>,
) {
    let light_count = query.iter().count();

    // Debug output every 5 seconds to confirm lights are active
    if time.elapsed_secs() % 5.0 < 0.1 {
        println!("🔥 STRESS TEST: {} lights actively animating!", light_count);
    }

    for (mut transform, mut light, stress_light) in query.iter_mut() {
        let elapsed = time.elapsed_secs();

        // More dramatic chaotic movement around base position
        let movement_x = (elapsed * stress_light.movement_speed).sin() * stress_light.movement_radius;
        let movement_y = (elapsed * stress_light.movement_speed * 1.3).cos() * stress_light.movement_radius;
        let movement_z = (elapsed * stress_light.movement_speed * 0.7).sin() * stress_light.movement_radius;

        transform.translation = stress_light.base_position + Vec3::new(movement_x, movement_y, movement_z);

        // More vibrant, rapid color cycling
        let color_time = elapsed * stress_light.color_cycle_speed;
        light.color = Color::srgb(
            0.3 + (color_time).sin().abs() * 0.7,           // More saturated reds
            0.3 + (color_time * 1.7).sin().abs() * 0.7,     // More saturated greens
            0.3 + (color_time * 2.3).sin().abs() * 0.7,     // More saturated blues
        );

        // Higher intensity variation for more dramatic effect
        let intensity_factor = (elapsed * 4.0 + stress_light.base_position.x + stress_light.base_position.y).sin() * stress_light.intensity_variation + 0.7;
        light.intensity = 1000.0 * intensity_factor.max(0.3); // Minimum 300, maximum 1700 intensity
    }
}

/// Handle input for switching demo modes
fn handle_input(
    keyboard_input: Res<ButtonInput<KeyCode>>,
    mut next_state: ResMut<NextState<DemoMode>>,
    mut fps_stats: ResMut<FpsStats>,
    time: Res<Time>,
) {
    if keyboard_input.just_pressed(KeyCode::Digit1) {
        next_state.set(DemoMode::Default);
        reset_fps_stats(&mut fps_stats, &time);
    }

    if keyboard_input.just_pressed(KeyCode::Digit2) {
        next_state.set(DemoMode::Explosions);
        reset_fps_stats(&mut fps_stats, &time);
    }

    if keyboard_input.just_pressed(KeyCode::Digit3) {
        next_state.set(DemoMode::Orbital);
        reset_fps_stats(&mut fps_stats, &time);
    }

    if keyboard_input.just_pressed(KeyCode::Digit4) {
        next_state.set(DemoMode::StressTest);
        reset_fps_stats(&mut fps_stats, &time);
    }

    if keyboard_input.just_pressed(KeyCode::Digit5) {
        next_state.set(DemoMode::Thrusters);
        reset_fps_stats(&mut fps_stats, &time);
    }

    if keyboard_input.just_pressed(KeyCode::Digit6) {
        next_state.set(DemoMode::PointLightOrbs);
        reset_fps_stats(&mut fps_stats, &time);
    }

    // Reset stats with R key
    if keyboard_input.just_pressed(KeyCode::KeyR) {
        reset_fps_stats(&mut fps_stats, &time);
        println!("📊 FPS Stats Reset!");
    }
}

/// Handle stress test specific controls (only active in stress test mode)
fn handle_stress_test_controls(
    keyboard_input: Res<ButtonInput<KeyCode>>,
    current_state: Res<State<DemoMode>>,
    mut stress_config: ResMut<StressTestConfig>,
    mut commands: Commands,
    stress_lights: Query<Entity, With<StressTestLight>>,
) {
    // Only handle stress test controls when in stress test mode
    if *current_state.get() != DemoMode::StressTest {
        return;
    }

    let mut lights_changed = false;

    // Add 50 lights with + key
    if keyboard_input.just_pressed(KeyCode::Equal) || keyboard_input.just_pressed(KeyCode::NumpadAdd) {
        stress_config.light_count += 50;
        spawn_additional_stress_lights(&mut commands, 50);
        lights_changed = true;
        println!("➕ Added 50 lights! Total: {}", stress_config.light_count);
    }

    // Remove 50 lights with - key
    if keyboard_input.just_pressed(KeyCode::Minus) || keyboard_input.just_pressed(KeyCode::NumpadSubtract) {
        if stress_config.light_count >= 50 {
            stress_config.light_count -= 50;
            remove_stress_lights(&mut commands, &stress_lights, 50);
            lights_changed = true;
            println!("➖ Removed 50 lights! Total: {}", stress_config.light_count);
        } else {
            println!("⚠️ Cannot remove more lights! Minimum is 0.");
        }
    }

    if lights_changed {
        stress_config.max_lights_spawned = stress_config.max_lights_spawned.max(stress_config.light_count);
        println!("🔥 STRESS TEST: {} lights active (Max reached: {})",
                stress_config.light_count, stress_config.max_lights_spawned);
    }
}

fn reset_fps_stats(fps_stats: &mut FpsStats, time: &Time) {
    fps_stats.min_fps = f64::INFINITY;
    fps_stats.max_fps = 0.0;
    fps_stats.frame_count = 0;
    fps_stats.fps_sum = 0.0;
    fps_stats.avg_fps = 0.0;
    fps_stats.last_reset_time = time.elapsed_secs_f64();
}

fn spawn_additional_stress_lights(commands: &mut Commands, count: u32) {
    use rand::Rng;
    let mut rng = rand::thread_rng();

    for _ in 0..count {
        let base_position = Vec3::new(
            rng.gen_range(-6.0..6.0),  // Smaller spread for better performance
            rng.gen_range(-4.0..4.0),
            rng.gen_range(-3.0..6.0),
        );

        let color = Color::srgb(
            rng.gen_range(0.4..1.0),
            rng.gen_range(0.4..1.0),
            rng.gen_range(0.4..1.0),
        );

        let movement_radius = rng.gen_range(0.3..1.5);
        let movement_speed = rng.gen_range(0.8..3.0);

        // Create PointLightOrb with floating movement for stress test
        commands.spawn((
            create_moving_point_light_orb(
                color,
                rng.gen_range(600.0..1200.0), // Lower intensity for performance
                rng.gen_range(0.06..0.12),    // Smaller orbs
                base_position,
                true, // Enable animation
                MovementPattern::Float {
                    center: base_position,
                    range: movement_radius,
                    speed: movement_speed,
                },
            ),
            StressTestLight {
                base_position,
                movement_radius,
                movement_speed,
                color_cycle_speed: rng.gen_range(1.5..4.0),
                intensity_variation: rng.gen_range(0.3..0.7),
            },
        ));
    }
}

fn remove_stress_lights(
    commands: &mut Commands,
    stress_lights: &Query<Entity, With<StressTestLight>>,
    count: u32
) {
    let mut removed = 0;
    for entity in stress_lights.iter() {
        if removed >= count {
            break;
        }
        commands.entity(entity).despawn(); // This will automatically clean up PointLightOrb children
        removed += 1;
    }
}

/// Handle explosion lighting effects using PointLightOrbs
fn handle_explosions(
    time: Res<Time>,
    mut explosion_timer: ResMut<ExplosionTimer>,
    mut commands: Commands,
    mut explosion_orbs: Query<(Entity, &mut PointLightOrb, &mut ExplosionLight)>,
) {
    explosion_timer.timer += time.delta_secs();

    // Spawn new explosions
    if explosion_timer.timer >= explosion_timer.next_explosion_time {
        use rand::Rng;
        let mut rng = rand::thread_rng();

        // Random position
        let x = rng.gen_range(-6.0..6.0);
        let y = rng.gen_range(-4.0..4.0);
        let z = rng.gen_range(-1.0..4.0);

        // More vibrant, saturated colors
        let color_type = rng.gen_range(0..4);
        let (r, g, b) = match color_type {
            0 => (1.0, rng.gen_range(0.1..0.4), rng.gen_range(0.0..0.2)), // Intense red/orange
            1 => (rng.gen_range(0.0..0.3), rng.gen_range(0.2..0.6), 1.0), // Electric blue/cyan
            2 => (rng.gen_range(0.1..0.4), 1.0, rng.gen_range(0.0..0.3)), // Neon green
            _ => (1.0, rng.gen_range(0.0..0.3), 1.0), // Magenta/purple
        };

        // Optimized intensity and lifetime for PointLightOrbs
        let max_intensity = rng.gen_range(2000.0..4000.0);
        let lifetime = rng.gen_range(0.5..1.5);
        let radius = rng.gen_range(0.15..0.25);

        // Create explosion PointLightOrb
        let mut explosion_orb = create_moving_point_light_orb(
            Color::srgb(r, g, b),
            0.0, // Start at 0 intensity
            radius,
            Vec3::new(x, y, z),
            false, // No flickering for explosions
            MovementPattern::Static,
        );

        // Override some settings for explosions
        explosion_orb.orb.range = 12.0; // Larger range for dramatic effect
        explosion_orb.orb.base_intensity = max_intensity;

        commands.spawn((
            explosion_orb,
            ExplosionLight {
                lifetime: 0.0,
                max_lifetime: lifetime,
                max_intensity,
            },
        ));

        // Set next explosion time
        explosion_timer.next_explosion_time = rng.gen_range(0.2..1.5);
        explosion_timer.timer = 0.0;
    }

    // Update existing explosions
    for (entity, mut orb, mut explosion) in explosion_orbs.iter_mut() {
        explosion.lifetime += time.delta_secs();

        if explosion.lifetime >= explosion.max_lifetime {
            // Remove expired explosions
            commands.entity(entity).despawn();
        } else {
            // Calculate explosion intensity curve (quick rise, slow fade)
            let progress = explosion.lifetime / explosion.max_lifetime;
            let intensity_factor = if progress < 0.1 {
                // Quick rise
                progress / 0.1
            } else {
                // Slow fade
                1.0 - ((progress - 0.1) / 0.9).powf(0.5)
            };

            orb.intensity = explosion.max_intensity * intensity_factor;
            orb.base_intensity = orb.intensity;
        }
    }
}

/// State transition systems
fn enter_default_mode(
    explosion_lights: Query<Entity, With<ExplosionLight>>,
    mut commands: Commands,
) {
    // Remove all explosion lights (PointLightOrbs will be spawned fresh)
    for entity in explosion_lights.iter() {
        commands.entity(entity).despawn();
    }

    println!("🌟 DEFAULT MODE: PointLightOrbs will be spawned automatically");
}

fn exit_default_mode(
    mut commands: Commands,
    point_light_orbs: Query<Entity, With<PointLightOrb>>,
) {
    // Clean up all PointLightOrbs
    for entity in point_light_orbs.iter() {
        commands.entity(entity).despawn();
    }

    println!("🧹 Cleaned up default mode PointLightOrbs");
}

fn enter_explosion_mode(
    point_light_orbs: Query<Entity, (With<PointLightOrb>, Without<ExplosionLight>)>,
    mut explosion_timer: ResMut<ExplosionTimer>,
    mut commands: Commands,
) {
    // Remove non-explosion PointLightOrbs
    for entity in point_light_orbs.iter() {
        commands.entity(entity).despawn();
    }

    // Reset explosion timer
    explosion_timer.timer = 0.0;
    explosion_timer.next_explosion_time = 0.3;

    println!("💥 EXPLOSION MODE: Cleared existing orbs, explosions will spawn automatically");
}

fn exit_explosion_mode(
    explosion_lights: Query<Entity, With<ExplosionLight>>,
    mut commands: Commands,
) {
    // Clean up explosion PointLightOrbs
    for entity in explosion_lights.iter() {
        commands.entity(entity).despawn();
    }

    println!("🧹 Cleaned up explosion mode PointLightOrbs");
}

/// Enter orbital mode - spawn PointLightOrbs that orbit on different axis planes
fn enter_orbital_mode(
    mut commands: Commands,
    existing_lights: Query<Entity, Or<(With<PointLightOrb>, With<ExplosionLight>, With<StressTestLight>)>>,
) {
    // Clean up existing lights
    for entity in existing_lights.iter() {
        commands.entity(entity).despawn();
    }

    // Spawn orbital PointLightOrbs on XY plane (4 orbs for performance)
    for i in 0..4 {
        let phase = (i as f32) * std::f32::consts::PI * 2.0 / 4.0;
        let center = Vec3::new(0.0, 0.0, 0.0);
        let radius = 3.5;
        let speed = 1.0;

        // Calculate initial position
        let angle = phase;
        let initial_pos = center + Vec3::new(
            radius * angle.cos(),
            radius * angle.sin(),
            0.0,
        );

        commands.spawn(create_moving_point_light_orb(
            Color::srgb(1.0, 0.5, 0.2),
            1000.0,
            0.12,
            initial_pos,
            true,
            MovementPattern::Orbit {
                center,
                radius,
                speed,
            },
        ));
    }

    // Spawn orbital PointLightOrbs on XZ plane (4 orbs)
    for i in 0..4 {
        let phase = (i as f32) * std::f32::consts::PI * 2.0 / 4.0;
        let center = Vec3::new(0.0, 0.0, 0.0);
        let radius = 3.0;
        let speed = -0.8;

        // Calculate initial position
        let angle = phase;
        let initial_pos = center + Vec3::new(
            radius * angle.cos(),
            0.0,
            radius * angle.sin(),
        );

        commands.spawn(create_moving_point_light_orb(
            Color::srgb(0.2, 1.0, 0.5),
            900.0,
            0.11,
            initial_pos,
            true,
            MovementPattern::Orbit {
                center,
                radius,
                speed,
            },
        ));
    }

    // Spawn orbital PointLightOrbs on YZ plane (4 orbs)
    for i in 0..4 {
        let phase = (i as f32) * std::f32::consts::PI * 2.0 / 4.0;
        let center = Vec3::new(0.0, 0.0, 0.0);
        let radius = 2.5;
        let speed = 1.2;

        // Calculate initial position
        let angle = phase;
        let initial_pos = center + Vec3::new(
            0.0,
            radius * angle.cos(),
            radius * angle.sin(),
        );

        commands.spawn(create_moving_point_light_orb(
            Color::srgb(0.5, 0.2, 1.0),
            800.0,
            0.10,
            initial_pos,
            true,
            MovementPattern::Orbit {
                center,
                radius,
                speed,
            },
        ));
    }

    println!("🌌 ORBITAL MODE: Spawned 12 PointLightOrbs orbiting on 3 axis planes");
    println!("   🟠 4 Orange orbs on XY plane (radius 3.5)");
    println!("   🟢 4 Green orbs on XZ plane (radius 3.0)");
    println!("   🟣 4 Purple orbs on YZ plane (radius 2.5)");
    println!("   ⚡ Optimized with no shadows for better performance");
}

fn exit_orbital_mode(
    point_light_orbs: Query<Entity, With<PointLightOrb>>,
    mut commands: Commands,
) {
    // Clean up all PointLightOrbs
    for entity in point_light_orbs.iter() {
        commands.entity(entity).despawn();
    }

    println!("🧹 Cleaned up orbital mode PointLightOrbs");
}

/// Enter stress test mode - spawn configurable number of lights
fn enter_stress_test_mode(
    mut commands: Commands,
    existing_lights: Query<Entity, Or<(With<PointLightOrb>, With<ExplosionLight>)>>,
    mut stress_config: ResMut<StressTestConfig>,
) {
    // Clean up existing lights
    for entity in existing_lights.iter() {
        commands.entity(entity).despawn();
    }

    let initial_lights = stress_config.light_count;
    println!("🚀 STRESS TEST: Spawning {} lights for maximum visual impact!", initial_lights);

    spawn_additional_stress_lights(&mut commands, initial_lights);
    stress_config.max_lights_spawned = initial_lights;

    println!("🎆 STRESS TEST ACTIVE: {} lights spawned! Prepare for visual overload!", initial_lights);
    println!("📊 Watch the FPS monitor (top-right) to see performance impact:");
    println!("   🟢 Green = Good performance (>45 FPS)");
    println!("   🟡 Yellow = OK performance (30-45 FPS)");
    println!("   🔴 Red = Poor performance (<30 FPS)");
    println!("🎮 Controls:");
    println!("   ➕ + (Plus) = Add 50 lights");
    println!("   ➖ - (Minus) = Remove 50 lights");
    println!("   📊 R = Reset FPS stats");
}

fn exit_stress_test_mode(
    stress_lights: Query<Entity, With<StressTestLight>>,
    point_light_orbs: Query<Entity, With<PointLightOrb>>,
    mut commands: Commands,
) {
    let light_count = stress_lights.iter().count();
    let orb_count = point_light_orbs.iter().count();
    println!("🧹 STRESS TEST: Cleaning up {} PointLightOrb entities...", orb_count);

    // Clean up stress test PointLightOrbs (they include the StressTestLight component)
    for entity in stress_lights.iter() {
        commands.entity(entity).despawn(); // Automatically cleans up children
    }

    println!("✅ STRESS TEST: All PointLightOrbs cleaned up!");
}

/// Update UI text to show current demo mode
fn update_ui_text(
    current_state: Res<State<DemoMode>>,
    mut text_query: Query<&mut Text>,
) {
    if current_state.is_changed() {
        for mut text in text_query.iter_mut() {
            let mode_text = match current_state.get() {
                DemoMode::Default => "Default Mode (Moving Lights)",
                DemoMode::Explosions => "Explosion Mode",
                DemoMode::Orbital => "Orbital Mode (3D Circles)",
                DemoMode::StressTest => "Stress Test (Hundreds of Lights)",
                DemoMode::Thrusters => "Thruster System (Shaders + Lights)",
                DemoMode::PointLightOrbs => "PointLightOrb Test",
            };
            **text = format!(
                "Demo Controls:\n1 - Default Mode (Moving Lights)\n2 - Explosion Mode\n3 - Orbital Mode (3D Circles)\n4 - Stress Test (Dynamic Lights)\n5 - Thruster System (Shaders + Lights)\n6 - PointLightOrb Test\n\nStress Test Controls (Mode 4 only):\n+ (Plus) - Add 50 lights\n- (Minus) - Remove 50 lights\nR - Reset stats\n\nThruster Controls (Mode 5 only):\nSpace - Increase power\nShift - Decrease power\n\nFPS Monitor: Top-right corner\nGreen = Good (>45 FPS)\nYellow = OK (30-45 FPS)\nRed = Poor (<30 FPS)\n\nCurrent: {}",
                mode_text
            );
        }
    }
}



/// This system updates the time uniform in the extended material
fn update_material_time(
    time: Res<Time>,
    mut materials: ResMut<Assets<MyMaterial>>,
) {
    for (_, material) in materials.iter_mut() {
        material.extension.time = time.elapsed_secs();
    }
}

/// Update comprehensive FPS statistics
fn update_fps_stats(
    diagnostics: Res<DiagnosticsStore>,
    mut fps_stats: ResMut<FpsStats>,
) {
    if let Some(fps) = diagnostics.get(&FrameTimeDiagnosticsPlugin::FPS) {
        if let Some(value) = fps.smoothed() {
            fps_stats.current_fps = value;
            fps_stats.frame_count += 1;
            fps_stats.fps_sum += value;

            // Update min/max
            fps_stats.min_fps = fps_stats.min_fps.min(value);
            fps_stats.max_fps = fps_stats.max_fps.max(value);

            // Calculate average
            fps_stats.avg_fps = fps_stats.fps_sum / fps_stats.frame_count as f64;
        }
    }
}

/// Update FPS display with comprehensive performance stats
fn update_fps_display(
    fps_stats: Res<FpsStats>,
    stress_config: Res<StressTestConfig>,
    current_state: Res<State<DemoMode>>,
    mut query: Query<(&mut Text, &mut TextColor), With<FpsText>>,
) {
    for (mut text, mut color) in &mut query {
        let current_fps = fps_stats.current_fps;

        // Show light count only in stress test mode
        let light_info = if *current_state.get() == DemoMode::StressTest {
            format!("\nLights: {}\nMax: {}", stress_config.light_count, stress_config.max_lights_spawned)
        } else {
            String::new()
        };

        // Update comprehensive FPS text
        **text = format!(
            "FPS: {:.1}\nMin: {:.1}\nMax: {:.1}\nAvg: {:.1}{}",
            current_fps,
            if fps_stats.min_fps == f64::INFINITY { 0.0 } else { fps_stats.min_fps },
            fps_stats.max_fps,
            fps_stats.avg_fps,
            light_info
        );

        // Update color based on current performance
        *color = if current_fps >= 45.0 {
            TextColor(Color::srgb(0.0, 1.0, 0.0)) // Green for good performance
        } else if current_fps >= 30.0 {
            TextColor(Color::srgb(1.0, 1.0, 0.0)) // Yellow for OK performance
        } else {
            TextColor(Color::srgb(1.0, 0.0, 0.0)) // Red for poor performance
        };
    }
}

/// Component to mark visual orb entities and link them to their lights
#[derive(Component)]
struct LightOrbVisual {
    parent_light: Entity,
}

/// System to create visual orbs for lights that should be visible
fn create_light_orbs(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    lights_without_orbs: Query<(Entity, &PointLight, &Transform), (With<LightOrb>, Without<LightOrbVisual>)>,
) {
    for (light_entity, light, transform) in lights_without_orbs.iter() {
        // Create a small glowing sphere to represent the light
        let orb_entity = commands.spawn((
            Mesh3d(meshes.add(Sphere::new(0.15))), // Slightly larger for better visibility
            MeshMaterial3d(materials.add(StandardMaterial {
                base_color: light.color.into(),
                emissive: LinearRgba::from(light.color) * 3.0, // Brighter glow
                unlit: true, // Don't let other lights affect it
                ..default()
            })),
            Transform::from_translation(transform.translation),
            LightOrbVisual { parent_light: light_entity },
        )).id();

        // Mark the light as having an orb (use the orb entity as the marker)
        commands.entity(light_entity).insert(LightOrbVisual { parent_light: orb_entity });
    }
}

/// System to update light orb colors and positions to match their lights
fn update_light_orb_colors(
    mut orb_materials: ResMut<Assets<StandardMaterial>>,
    mut orb_query: Query<(&mut Transform, &MeshMaterial3d<StandardMaterial>, &LightOrbVisual), With<LightOrbVisual>>,
    light_query: Query<(&Transform, &PointLight), (With<LightOrb>, Without<LightOrbVisual>)>,
) {
    for (mut orb_transform, orb_material, orb_visual) in orb_query.iter_mut() {
        // Find the corresponding light entity
        if let Ok((light_transform, light)) = light_query.get(orb_visual.parent_light) {
            // Update orb position to match light
            orb_transform.translation = light_transform.translation;

            // Update orb color to match light
            if let Some(material) = orb_materials.get_mut(&orb_material.0) {
                material.base_color = light.color.into();
                material.emissive = LinearRgba::from(light.color) * 2.0;
            }
        }
    }
}

/// Manual cleanup function for all orbs (called during mode transitions)
fn cleanup_all_orbs(commands: &mut Commands, orb_query: &Query<Entity, With<LightOrbVisual>>) {
    for orb_entity in orb_query.iter() {
        if let Ok(mut entity_commands) = commands.get_entity(orb_entity) {
            entity_commands.despawn();
        }
    }
}

/// This system slowly rotates the sprite quad so we can see it from different angles.
fn rotate_sprite(
    time: Res<Time>,
    mut query: Query<&mut Transform, With<SpriteQuad>>,
) {
    for mut transform in query.iter_mut() {
        transform.rotation *= Quat::from_rotation_z(time.delta_secs() * 0.2);
    }
}

/// Spawn demo PointLightOrbs when entering the mode
fn spawn_point_light_orb_demo(mut commands: Commands) {
    println!("🔮 POINT LIGHT ORB DEMO: Spawning moving synchronized light orbs!");

    // Spawn fewer PointLightOrbs positioned to interact with the sprite (sprite is at 0,0,0)
    let orb_configs = vec![
        // Red - Orbiting above the sprite
        (
            Vec3::new(0.0, 2.5, 1.0),
            Color::srgb(1.0, 0.2, 0.1),
            1200.0,
            0.12,
            MovementPattern::Orbit {
                center: Vec3::new(0.0, 2.5, 0.0),
                radius: 1.8,
                speed: 1.2
            }
        ),
        // Green - Figure-8 above and around sprite
        (
            Vec3::new(0.0, 1.5, 2.0),
            Color::srgb(0.1, 1.0, 0.2),
            1000.0,
            0.10,
            MovementPattern::Figure8 {
                center: Vec3::new(0.0, 1.5, 1.0),
                scale: 1.0,
                speed: 0.9
            }
        ),
        // Blue - Linear movement across the sprite
        (
            Vec3::new(-2.0, 1.0, 1.5),
            Color::srgb(0.1, 0.3, 1.0),
            1400.0,
            0.11,
            MovementPattern::Linear {
                start: Vec3::new(-2.5, 1.0, 1.5),
                end: Vec3::new(2.5, 1.0, 1.5),
                speed: 1.0
            }
        ),
        // Yellow - Floating above sprite center
        (
            Vec3::new(0.0, 3.0, 0.5),
            Color::srgb(1.0, 0.8, 0.1),
            800.0,
            0.09,
            MovementPattern::Float {
                center: Vec3::new(0.0, 3.0, 0.5),
                range: 0.8,
                speed: 1.3
            }
        ),
    ];

    let orb_count = orb_configs.len();
    for (position, color, intensity, radius, movement) in orb_configs {
        commands.spawn(create_moving_point_light_orb(
            color,
            intensity,
            radius,
            position,
            true, // Enable animation
            movement,
        ));
    }

    println!("   ✨ Spawned {} optimized PointLightOrbs positioned above sprite", orb_count);
    println!("   🎯 Performance optimizations:");
    println!("   ⚡ Shadows disabled for better FPS");
    println!("   📉 Reduced light ranges (8.0 vs 20.0)");
    println!("   🎛️ Lower intensities (800-1400 vs 1500-3000)");
    println!("   🔄 Reduced update frequency for materials");
    println!("   🎯 Movement patterns:");
    println!("   🔴 Red: Circular orbit above sprite");
    println!("   🟢 Green: Figure-8 around sprite");
    println!("   🔵 Blue: Linear sweep across sprite");
    println!("   🟡 Yellow: Gentle floating above center");
    println!("   💡 All lights positioned to illuminate the sprite!");
    println!("   🌟 Semi-transparent soft glow effect");
}

/// Cleanup PointLightOrbs when exiting the mode
fn cleanup_point_light_orb_demo(
    mut commands: Commands,
    orb_query: Query<Entity, With<PointLightOrb>>,
) {
    for entity in orb_query.iter() {
        commands.entity(entity).despawn();
    }
    println!("🧹 Cleaned up PointLightOrb demo entities");
}
