# Fleeting

A Bevy-based game development workspace with prototyping tools and GitFlow workflow.

## 🚀 Quick Start

### Prerequisites

- [Rust](https://rustup.rs/) (latest stable)
- [Git](https://git-scm.com/)

### Setup

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd fleeting
   ```

2. Build and run the main project:

   ```bash
   cargo run
   ```

3. Run specific prototypes:

   ```bash
   # Lighting diorama prototype
   cargo run -p lighting_diorama

   # Dual-mode control prototype
   cargo run -p dual_mode
   ```

## 📁 Project Structure

```
fleeting/
├── src/                    # Main game source code
│   ├── main.rs            # Entry point
│   ├── systems/           # Game systems
│   ├── components/        # ECS components
│   └── resources/         # Game resources
├── assets/                # Game assets
│   ├── textures/          # Texture files
│   ├── models/            # 3D models
│   ├── sounds/            # Audio files
│   └── shaders/           # Custom shaders
├── prototypes/            # Experimental prototypes
│   ├── diorama/           # Lighting system prototype
│   └── dual_mode/         # Dual-mode control prototype
├── docs/                  # Documentation
└── Cargo.toml            # Workspace configuration
```

## 🔧 Development

### Building

```bash
# Debug build (faster compilation)
cargo build

# Release build (optimized)
cargo build --release

# Build specific prototype
cargo build -p lighting_diorama
```

### Running Tests

```bash
# Run all tests
cargo test

# Run tests for specific package
cargo test -p lighting_diorama
```

### Performance Monitoring

The project includes FpsOverlayPlugin for performance monitoring. Press `F3` in-game to toggle the FPS display.

## 🌿 GitFlow Workflow

This project uses GitFlow for branch management:

- **main**: Production-ready releases
- **develop**: Integration branch for ongoing development
- **feature/**: New features (`feature/player-movement`)
- **release/**: Release preparation (`release/v0.1.0`)
- **hotfix/**: Critical fixes (`hotfix/crash-on-startup`)

### Common Commands

```bash
# Start a new feature
git checkout develop
git checkout -b feature/my-new-feature

# Finish a feature (merge to develop)
git checkout develop
git merge feature/my-new-feature
git branch -d feature/my-new-feature

# Create a release
git checkout develop
git checkout -b release/v0.1.0
# ... make release preparations
git checkout main
git merge release/v0.1.0
git tag v0.1.0
git checkout develop
git merge release/v0.1.0
```

## 🎮 Prototypes

### Lighting Diorama

Demonstrates advanced lighting systems with:

- Point light orbs with movement patterns
- Custom materials and shaders
- Performance stress testing
- Real-time lighting effects

### Dual Mode

Tests dual control schemes:

- Pilot Mode: Twin-stick shooter controls
- Tactical Mode: RTS-style controls
- Smooth transition with time scaling

## 📚 Documentation

- [GitFlow Guide](docs/GITFLOW.md)
- [Development Setup](docs/DEVELOPMENT.md)
- [Branching Strategy](docs/BRANCHING.md)

## 🤝 Contributing

1. Follow the GitFlow workflow
2. Create feature branches from `develop`
3. Write tests for new functionality
4. Update documentation as needed
5. Submit pull requests to `develop`

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Dependencies

- [Bevy](https://bevyengine.org/) - Game engine
- [rand](https://crates.io/crates/rand) - Random number generation
- [log](https://crates.io/crates/log) - Logging framework

## 🎯 Roadmap

- [ ] Core gameplay systems
- [ ] Asset pipeline
- [ ] Audio system integration
- [ ] Networking capabilities
- [ ] Platform-specific builds
