//! Dual-Mode Command System Prototype: "Flow State" Control Test
//!
//! This prototype tests the fundamental feel and responsiveness of switching between
//! twin-stick shooter controls (Pilot Mode) and tactical RTS controls (Tactical Mode)
//! with a smooth "Tactical Slowdown" transition.

use std::f32::consts::TAU;

use bevy::prelude::*;

use bevy_rts_camera::{Ground, RtsCamera, RtsCameraControls, RtsCameraPlugin, RtsCameraSystemSet};

// Game states for the dual-mode system
#[derive(States, Debug, Clone, PartialEq, Eq, Hash, Default)]
enum GameMode {
    #[default]
    PilotMode,
    TacticalSlowdown,
    TacticalMode,
}

// Time scaling resource for smooth slowdown
#[derive(Resource)]
struct TimeScaling {
    target_scale: f32,
    current_scale: f32,
    transition_speed: f32,
}

impl Default for TimeScaling {
    fn default() -> Self {
        Self {
            target_scale: 1.0,
            current_scale: 1.0,
            transition_speed: 1.0, // Complete transition in 1 second
        }
    }
}

fn main() {
    App::new()
        .add_plugins(DefaultPlugins)
        .add_plugins(RtsCameraPlugin)
        .init_state::<GameMode>()
        .init_resource::<TimeScaling>()
        .add_systems(Startup, setup)
        .add_systems(
            Update,
            (
                mode_transition_input,
                time_scaling_system,
                move_unit.run_if(in_state(GameMode::PilotMode).or(in_state(GameMode::TacticalSlowdown))),
                pilot_ship_control.run_if(in_state(GameMode::PilotMode)),
                tactical_ship_selection.run_if(in_state(GameMode::TacticalMode)),
                tactical_move_command.run_if(in_state(GameMode::TacticalMode)),
                execute_move_commands,
                toggle_camera_controls_by_mode,
                (lock_or_jump, toggle_controls)
            )
                .chain()
                .before(RtsCameraSystemSet),
        )
        .run();
}

// Component markers
#[derive(Component)]
struct Move;

#[derive(Component)]
struct PlayerShip {
    speed: f32,
    rotation_speed: f32,
}

#[derive(Component)]
struct NpcShip {
    speed: f32,
}

#[derive(Component)]
struct Selected;

#[derive(Component)]
struct MoveTarget {
    target: Vec3,
    speed: f32,
}

fn setup(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    // Ground
    commands.spawn((
        Mesh3d(meshes.add(Plane3d::default().mesh().size(80.0, 80.0))),
        MeshMaterial3d(materials.add(Color::srgb(0.3, 0.5, 0.3))),
        // Add `Ground` component to any entity you want the camera to treat as ground.
        Ground,
    ));
    // Some "terrain"
    let terrain_material = materials.add(Color::srgb(0.8, 0.7, 0.6));
    commands.spawn((
        Mesh3d(meshes.add(Cuboid::new(15.0, 1.0, 5.0))),
        MeshMaterial3d(terrain_material.clone()),
        Transform::from_xyz(15.0, 0.5, -5.0),
        Ground,
    ));
    commands.spawn((
        Mesh3d(meshes.add(Cuboid::new(10.0, 5.0, 15.0))),
        MeshMaterial3d(terrain_material.clone()),
        Transform::from_xyz(-15.0, 2.5, 0.0),
        Ground,
    ));
    commands.spawn((
        Mesh3d(meshes.add(Sphere::new(12.5))),
        MeshMaterial3d(terrain_material.clone()),
        Transform::from_xyz(0.0, 0.0, -23.0),
        Ground,
    ));
    // Some generic units that are not part of the 'Ground' (ignored for height calculation)
    for x in -5..5 {
        for z in -5..5 {
            commands.spawn((
                Mesh3d(meshes.add(Capsule3d::new(0.25, 1.25))),
                MeshMaterial3d(terrain_material.clone()),
                Transform::from_xyz(x as f32 * 0.7, 0.75, z as f32 * 0.7),
            ));
        }
    }
    // A moving unit that can be locked onto
    commands
        .spawn((
            Mesh3d(meshes.add(Capsule3d::new(0.25, 1.25))),
            MeshMaterial3d(terrain_material.clone()),
            Transform::from_xyz(0.0, 0.75, 0.0),
        ))
        .insert(Move);

    // Player ship - distinctive blue color and larger size
    let player_material = materials.add(Color::srgb(0.2, 0.4, 0.8));
    commands.spawn((
        Mesh3d(meshes.add(Capsule3d::new(0.4, 2.0))),
        MeshMaterial3d(player_material),
        Transform::from_xyz(-10.0, 1.0, -10.0),
        PlayerShip {
            speed: 15.0,
            rotation_speed: 3.0,
        },
    ));

    // NPC ships - green color, can be commanded in tactical mode
    let npc_material = materials.add(Color::srgb(0.2, 0.8, 0.2));
    commands.spawn((
        Mesh3d(meshes.add(Capsule3d::new(0.3, 1.5))),
        MeshMaterial3d(npc_material.clone()),
        Transform::from_xyz(-5.0, 0.75, -8.0),
        NpcShip { speed: 8.0 },
    ));

    commands.spawn((
        Mesh3d(meshes.add(Capsule3d::new(0.3, 1.5))),
        MeshMaterial3d(npc_material.clone()),
        Transform::from_xyz(-8.0, 0.75, -5.0),
        NpcShip { speed: 8.0 },
    ));
    // Light
    commands.spawn((
        DirectionalLight {
            illuminance: 1000.0,
            shadows_enabled: true,
            ..default()
        },
        Transform::from_rotation(Quat::from_euler(
            EulerRot::YXZ,
            150.0f32.to_radians(),
            -40.0f32.to_radians(),
            0.0,
        )),
    ));
    // Help text
    commands.spawn(Text::new(
        "\
DUAL-MODE PROTOTYPE (Top-Down 2D):
TAB: Toggle between Pilot/Tactical Mode (with 1-second slowdown)
Pilot Mode: WASD moves, Arrow keys rotate blue player ship
Tactical Mode: Time FROZEN - Click to select green NPC ships, Right-click to move them
Camera: IJKL to pan, Middle mouse to drag
Press K to jump to the moving unit, Hold L to lock onto it",
    ));
    // Camera - Top-down 2D view
    commands.spawn((
        RtsCamera {
            // Fixed top-down angle (90 degrees)
            angle: 90.0f32.to_radians(),
            target_angle: 90.0f32.to_radians(),
            // Center on player ship area
            focus: Transform::from_xyz(-10.0, 0.0, -10.0),
            // Set zoom level for good overview
            zoom: 0.3,
            ..default()
        },
        RtsCameraControls {
            // WASD camera controls - will be enabled/disabled based on game mode
            key_up: KeyCode::KeyW,
            key_down: KeyCode::KeyS,
            key_left: KeyCode::KeyA,
            key_right: KeyCode::KeyD,
            // Disable rotation for 2D top-down view
            button_rotate: MouseButton::Middle, // Move to middle mouse to avoid conflicts
            // Keep the mouse cursor in place when rotating
            lock_on_rotate: true,
            // Drag pan with middle click
            button_drag: Some(MouseButton::Middle),
            // Keep the mouse cursor in place when dragging
            lock_on_drag: true,
            // Disable edge pan to avoid conflicts with game controls
            edge_pan_width: 0.0,
            // Increase pan speed
            pan_speed: 25.0,
            // Start disabled - will be enabled in tactical mode
            enabled: false,
            ..default()
        },
    ));
}

// Handle mode transition input
fn mode_transition_input(
    keyboard: Res<ButtonInput<KeyCode>>,
    current_state: Res<State<GameMode>>,
    mut next_state: ResMut<NextState<GameMode>>,
    mut time_scaling: ResMut<TimeScaling>,
) {
    if keyboard.just_pressed(KeyCode::Tab) {
        match current_state.get() {
            GameMode::PilotMode => {
                // Start tactical slowdown
                next_state.set(GameMode::TacticalSlowdown);
                time_scaling.target_scale = 0.0;
            }
            GameMode::TacticalSlowdown => {
                // Cancel slowdown, return to pilot mode
                next_state.set(GameMode::PilotMode);
                time_scaling.target_scale = 1.0;
            }
            GameMode::TacticalMode => {
                // Exit tactical mode, return to pilot mode
                next_state.set(GameMode::PilotMode);
                time_scaling.target_scale = 1.0;
            }
        }
    }
}

// Smooth time scaling system
fn time_scaling_system(
    mut time_scaling: ResMut<TimeScaling>,
    mut virtual_time: ResMut<Time<Virtual>>,
    current_state: Res<State<GameMode>>,
    mut next_state: ResMut<NextState<GameMode>>,
    time: Res<Time<Real>>, // Use Real time to avoid feedback loop!
) {
    // Smoothly interpolate current scale towards target using REAL time
    let delta = time.delta_secs();
    let scale_diff = time_scaling.target_scale - time_scaling.current_scale;

    if scale_diff.abs() > 0.01 {
        time_scaling.current_scale += scale_diff * time_scaling.transition_speed * delta;
    } else {
        time_scaling.current_scale = time_scaling.target_scale;

        // Check if we've completed the slowdown transition
        if *current_state.get() == GameMode::TacticalSlowdown && time_scaling.current_scale <= 0.01 {
            next_state.set(GameMode::TacticalMode);
        }
    }

    // Apply the time scale
    virtual_time.set_relative_speed(time_scaling.current_scale);
}

// Twin-stick ship control in pilot mode
fn pilot_ship_control(
    keyboard: Res<ButtonInput<KeyCode>>,
    mut ship_query: Query<&mut Transform, With<PlayerShip>>,
    ship_data: Query<&PlayerShip>,
    time: Res<Time<Virtual>>,
) {
    if let (Ok(mut transform), Ok(ship)) = (ship_query.single_mut(), ship_data.single()) {
        let delta = time.delta_secs();

        // Movement (left stick simulation with WASD)
        let mut movement = Vec3::ZERO;
        if keyboard.pressed(KeyCode::KeyW) {
            movement.z -= 1.0;
        }
        if keyboard.pressed(KeyCode::KeyS) {
            movement.z += 1.0;
        }
        if keyboard.pressed(KeyCode::KeyA) {
            movement.x -= 1.0;
        }
        if keyboard.pressed(KeyCode::KeyD) {
            movement.x += 1.0;
        }

        // Apply movement relative to ship's current rotation
        if movement.length() > 0.0 {
            movement = movement.normalize();
            let rotated_movement = transform.rotation * movement;
            transform.translation += rotated_movement * ship.speed * delta;
        }

        // Rotation (right stick simulation with arrow keys)
        let mut rotation = 0.0;
        if keyboard.pressed(KeyCode::ArrowLeft) {
            rotation += 1.0;
        }
        if keyboard.pressed(KeyCode::ArrowRight) {
            rotation -= 1.0;
        }

        if rotation != 0.0 {
            transform.rotate_y(rotation * ship.rotation_speed * delta);
        }
    }
}

// Move a unit in a circle
fn move_unit(
    time: Res<Time<Virtual>>,
    mut cube_q: Query<&mut Transform, With<Move>>,
    mut angle: Local<f32>,
) {
    if let Ok(mut cube_tfm) = cube_q.single_mut() {
        // Rotate 20 degrees a second, wrapping around to 0 after a full rotation
        *angle += 20f32.to_radians() * time.delta_secs() % TAU;
        // Convert angle to position
        let pos = Vec3::new(angle.sin() * 7.5, 0.75, angle.cos() * 7.5);
        cube_tfm.translation = pos;
    }
}

// Tactical mode: NPC ship selection with mouse clicks
fn tactical_ship_selection(
    mouse_input: Res<ButtonInput<MouseButton>>,
    mut commands: Commands,
    npc_ship_query: Query<(Entity, &Transform), With<NpcShip>>,
    selected_query: Query<Entity, With<Selected>>,
    camera_query: Query<(&Camera, &GlobalTransform)>,
    windows: Query<&Window>,
) {
    if mouse_input.just_pressed(MouseButton::Left) {
        if let Ok(window) = windows.single() {
            if let Some(cursor_pos) = window.cursor_position() {
                if let Ok((camera, camera_transform)) = camera_query.single() {
                    // Simple 3D picking - cast ray from camera through cursor
                    if let Ok(ray) = camera.viewport_to_world(camera_transform, cursor_pos) {
                        // Check if ray intersects with any NPC ship (simplified)
                        for (ship_entity, ship_transform) in npc_ship_query.iter() {
                            let ship_pos = ship_transform.translation;
                            let distance_to_ray = ray.origin.distance(ship_pos);

                            // Simple sphere collision check (radius of 2.0)
                            if distance_to_ray < 2.0 {
                                // Deselect all previously selected entities
                                for entity in selected_query.iter() {
                                    commands.entity(entity).remove::<Selected>();
                                }

                                // Select this NPC ship
                                commands.entity(ship_entity).insert(Selected);
                                break;
                            }
                        }
                    }
                }
            }
        }
    }
}

// Tactical mode: Issue move commands to selected NPC ships with right-click
fn tactical_move_command(
    mouse_input: Res<ButtonInput<MouseButton>>,
    mut commands: Commands,
    selected_query: Query<(Entity, &NpcShip), With<Selected>>,
    camera_query: Query<(&Camera, &GlobalTransform)>,
    ground_query: Query<&GlobalTransform, With<Ground>>,
    windows: Query<&Window>,
) {
    if mouse_input.just_pressed(MouseButton::Right) {
        if let Ok(window) = windows.single() {
            if let Some(cursor_pos) = window.cursor_position() {
                if let Ok((camera, camera_transform)) = camera_query.single() {
                    // Cast ray to find ground intersection
                    if let Ok(ray) = camera.viewport_to_world(camera_transform, cursor_pos) {
                        // Simple ground plane intersection (y = 0)
                        let t = -ray.origin.y / ray.direction.y;
                        if t > 0.0 {
                            let target_pos = ray.origin + ray.direction * t;

                            // Issue move command to selected NPC ships
                            for (ship_entity, npc_ship) in selected_query.iter() {
                                commands.entity(ship_entity).insert(MoveTarget {
                                    target: target_pos,
                                    speed: npc_ship.speed,
                                });
                            }
                        }
                    }
                }
            }
        }
    }
}

// Execute move commands for NPC ships
fn execute_move_commands(
    mut commands: Commands,
    mut npc_ship_query: Query<(Entity, &mut Transform, &MoveTarget), With<NpcShip>>,
    time: Res<Time<Virtual>>,
) {
    for (entity, mut transform, move_target) in npc_ship_query.iter_mut() {
        let direction = move_target.target - transform.translation;
        let distance = direction.length();

        if distance > 0.5 {
            // Move towards target
            let movement = direction.normalize() * move_target.speed * time.delta_secs();
            transform.translation += movement;

            // Rotate to face movement direction
            if movement.length() > 0.01 {
                let target_rotation = Quat::from_rotation_y((-movement.x).atan2(-movement.z));
                transform.rotation = transform.rotation.slerp(target_rotation, 5.0 * time.delta_secs());
            }
        } else {
            // Reached target, remove move command
            commands.entity(entity).remove::<MoveTarget>();
        }
    }
}

// Either jump to the moving unit (press K) or lock onto it (hold L)
fn lock_or_jump(
    key_input: Res<ButtonInput<KeyCode>>,
    cube_q: Query<&Transform, With<Move>>,
    mut cam_q: Query<&mut RtsCamera>,
) {
    for cube in cube_q.iter() {
        for mut cam in cam_q.iter_mut() {
            if key_input.pressed(KeyCode::KeyL) {
                cam.target_focus.translation = cube.translation;
                cam.snap = true;
            }
            if key_input.just_pressed(KeyCode::KeyK) {
                cam.target_focus.translation = cube.translation;
                cam.target_zoom = 0.4;
            }
        }
    }
}

fn toggle_controls(
    mut controls_q: Query<&mut RtsCameraControls>,
    key_input: Res<ButtonInput<KeyCode>>,
) {
    for mut controls in controls_q.iter_mut() {
        if key_input.just_pressed(KeyCode::KeyT) {
            controls.enabled = !controls.enabled;
        }
    }
}