use bevy::prelude::*;
use bevy::render::render_resource::{AsBindGroup, ShaderRef};

/// Component for thruster entities that combine visual effects with lighting
#[derive(Component, Clone)]
pub struct Thruster {
    pub power: f32,           // 0.0 to 1.0 - controls both visual intensity and light brightness
    pub base_color: Color,    // Base color of the thruster flame
    pub flicker_speed: f32,   // How fast the thruster flickers
    pub size: f32,           // Size of the thruster effect
    pub light_intensity: f32, // Base light intensity
    pub light_range: f32,    // Range of the light effect
}

impl Default for Thruster {
    fn default() -> Self {
        Self {
            power: 1.0,
            base_color: Color::srgb(0.0, 0.5, 1.0), // Blue thruster
            flicker_speed: 8.0,
            size: 1.0,
            light_intensity: 5000.0,
            light_range: 15.0,
        }
    }
}

/// Custom material for thruster visual effects
#[derive(Asset, TypePath, AsBindGroup, Debug, Clone)]
pub struct ThrusterMaterial {
    #[uniform(0)]
    pub power: f32,
    #[uniform(0)]
    pub time: f32,
    #[uniform(0)]
    pub flicker_speed: f32,
    #[uniform(0)]
    pub base_color: LinearRgba,
    #[uniform(0)]
    pub size: f32,
}

impl Material for ThrusterMaterial {
    fn fragment_shader() -> ShaderRef {
        "shaders/thruster.wgsl".into()
    }
    
    fn alpha_mode(&self) -> AlphaMode {
        AlphaMode::Blend
    }
}

/// System to spawn thruster entities with both visual and lighting components
pub fn spawn_thrusters(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    _thruster_materials: ResMut<Assets<ThrusterMaterial>>,
    mut standard_materials: ResMut<Assets<StandardMaterial>>,
) {
    // Spawn multiple thrusters around the ship (all within camera view)
    let thruster_positions = vec![
        Vec3::new(-3.0, 0.0, -2.5),   // Left side
        Vec3::new(3.0, 0.0, -2.5),    // Right side
        Vec3::new(0.0, -3.0, -3.0),   // Bottom (main thruster)
        Vec3::new(-1.5, 2.0, -2.0),   // Left top
        Vec3::new(1.5, 2.0, -2.0),    // Right top
    ];

    for (i, position) in thruster_positions.iter().enumerate() {
        let thruster_config = Thruster {
            power: 0.8 + (i as f32 * 0.05), // Slight variation
            base_color: match i {
                0 => Color::srgb(1.0, 0.0, 0.0),     // Red left thruster
                1 => Color::srgb(0.0, 1.0, 0.0),     // Green right thruster
                2 => Color::srgb(1.0, 0.5, 0.0),     // Orange main thruster
                3 => Color::srgb(0.0, 0.5, 1.0),     // Blue left top
                4 => Color::srgb(1.0, 0.0, 1.0),     // Magenta right top
                _ => Color::srgb(1.0, 1.0, 1.0),     // White fallback
            },
            flicker_speed: 6.0 + (i as f32 * 2.0),
            size: if i == 2 { 1.5 } else { 1.0 }, // Main thruster is larger
            light_intensity: if i == 2 { 8000.0 } else { 4000.0 },
            light_range: if i == 2 { 20.0 } else { 12.0 },
        };

        // Create the visual thruster effect (using animated glowing material)
        let plane_size = thruster_config.size * 2.5; // Good size for visibility
        let material_handle = standard_materials.add(StandardMaterial {
            base_color: thruster_config.base_color.into(),
            emissive: LinearRgba::from(thruster_config.base_color) * 6.0, // Bright glow
            unlit: true, // Don't let other lights affect it
            alpha_mode: AlphaMode::Blend,
            ..default()
        });

        let _thruster_visual = commands.spawn((
            Mesh3d(meshes.add(Plane3d::default().mesh().size(plane_size, plane_size))),
            MeshMaterial3d(material_handle),
            Transform::from_translation(*position), // Keep facing camera
            thruster_config.clone(),
            ThrusterVisual, // Mark for animation updates
        )).id();

        println!("🔥 Spawned thruster {} at position {:?} with color {:?}",
                 i, position, thruster_config.base_color);

        // Create the dynamic light that represents the thruster's glow
        commands.spawn((
            PointLight {
                intensity: thruster_config.light_intensity * thruster_config.power,
                color: thruster_config.base_color,
                range: thruster_config.light_range,
                shadows_enabled: true,
                ..default()
            },
            Transform::from_translation(*position + Vec3::NEG_Z * 0.5), // Slightly behind the visual
            ThrusterLight,
        ));
    }

    println!("🚀 THRUSTER SYSTEM: Spawned {} thrusters with glowing effects + dynamic lighting!", thruster_positions.len());
    println!("   🔥 Each thruster combines:");
    println!("   📺 Bright glowing visual effects");
    println!("   💡 Dynamic point lights (scene interaction)");
    println!("   🎮 Real-time power control");
    println!("   🌈 Color-coded: Red, Green, Orange, Blue, Magenta");
    println!("   📍 Positions: {:?}", thruster_positions);
}

/// Component to mark thruster lights
#[derive(Component)]
pub struct ThrusterLight;

/// Component to mark thruster visual effects for animation
#[derive(Component)]
pub struct ThrusterVisual;

/// System to update thruster effects and lighting in real-time
pub fn update_thrusters(
    time: Res<Time>,
    mut thruster_query: Query<(&mut Thruster, &MeshMaterial3d<StandardMaterial>), With<ThrusterVisual>>,
    mut light_query: Query<&mut PointLight, With<ThrusterLight>>,
    mut standard_materials: ResMut<Assets<StandardMaterial>>,
) {
    let elapsed = time.elapsed_secs();

    // Update thruster visual effects
    for (thruster, material_handle) in thruster_query.iter_mut() {
        if let Some(material) = standard_materials.get_mut(&material_handle.0) {
            // Add flickering animation
            let flicker = (elapsed * thruster.flicker_speed).sin() * 0.3 + 0.7;
            let power_multiplier = thruster.power * flicker;

            // Update emissive intensity for flickering effect
            material.emissive = LinearRgba::from(thruster.base_color) * (6.0 * power_multiplier);

            // Slightly vary the base color for more dynamic effect
            let color_variation = (elapsed * thruster.flicker_speed * 1.3).sin() * 0.1 + 1.0;
            material.base_color = Color::LinearRgba(LinearRgba::from(thruster.base_color) * color_variation);
        }
    }

    // Update thruster lights to match visual effects
    for mut light in light_query.iter_mut() {
        // Apply flickering to lights
        let flicker = (elapsed * 8.0).sin() * 0.3 + 0.7;
        // Store base intensity if not already stored
        let base_intensity = 4000.0; // You might want to store this in the component
        light.intensity = base_intensity * flicker;
    }
}

/// System to handle thruster power control (example of dynamic control)
pub fn control_thruster_power(
    keyboard: Res<ButtonInput<KeyCode>>,
    mut thruster_query: Query<&mut Thruster>,
    time: Res<Time>,
) {
    let power_change = time.delta_secs() * 2.0; // Power change rate

    for mut thruster in thruster_query.iter_mut() {
        // Increase power with Space
        if keyboard.pressed(KeyCode::Space) {
            thruster.power = (thruster.power + power_change).min(1.0);
        }
        // Decrease power with Shift
        else if keyboard.pressed(KeyCode::ShiftLeft) {
            thruster.power = (thruster.power - power_change).max(0.0);
        }
        // Auto-stabilize to 80% power
        else {
            let target_power = 0.8;
            if thruster.power > target_power {
                thruster.power = (thruster.power - power_change * 0.5).max(target_power);
            } else if thruster.power < target_power {
                thruster.power = (thruster.power + power_change * 0.5).min(target_power);
            }
        }
    }
}

/// System to clean up thrusters when exiting thruster mode
pub fn cleanup_thrusters(
    mut commands: Commands,
    thruster_query: Query<Entity, With<Thruster>>,
    light_query: Query<Entity, With<ThrusterLight>>,
) {
    // Remove all thruster visuals
    for entity in thruster_query.iter() {
        commands.entity(entity).despawn();
    }
    
    // Remove all thruster lights
    for entity in light_query.iter() {
        commands.entity(entity).despawn();
    }
    
    println!("🧹 Cleaned up thruster system");
}
