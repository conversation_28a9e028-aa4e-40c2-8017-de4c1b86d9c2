# Development Guide

This guide covers the development setup, coding standards, and contribution process for the Fleeting project.

## 🛠️ Development Environment Setup

### Prerequisites

1. **Rust Toolchain**

   ```bash
   # Install Rust via rustup
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

   # Ensure you have the latest stable version
   rustup update stable
   ```

2. **Git Configuration**

   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

3. **IDE Setup** (Recommended: VS Code)
   - Install Rust Analyzer extension
   - Install GitLens extension
   - Configure auto-formatting on save

### Project Setup

```bash
# Clone the repository
git clone <repository-url>
cd fleeting

# Build the project
cargo build

# Run tests
cargo test

# Run the main application
cargo run

# Run specific prototypes
cargo run -p lighting_diorama
cargo run -p dual_mode
```

## 📋 Coding Standards

### Rust Code Style

We follow standard Rust conventions with these additions:

#### Formatting

- Use `cargo fmt` before committing
- Line length: 100 characters maximum
- Use 4 spaces for indentation (enforced by rustfmt)

#### Naming Conventions

- **Modules**: `snake_case` (e.g., `player_movement`)
- **Structs/Enums**: `PascalCase` (e.g., `PlayerController`)
- **Functions/Variables**: `snake_case` (e.g., `update_position`)
- **Constants**: `SCREAMING_SNAKE_CASE` (e.g., `MAX_HEALTH`)

#### Documentation

- All public items must have documentation comments
- Use `///` for item documentation
- Use `//!` for module-level documentation
- Include examples for complex functions

````rust
/// Represents a player entity with movement capabilities.
///
/// # Examples
///
/// ```
/// let player = Player::new(Vec3::ZERO, 100.0);
/// player.move_to(Vec3::new(10.0, 0.0, 0.0));
/// ```
pub struct Player {
    /// Current position in world space
    pub position: Vec3,
    /// Maximum movement speed in units per second
    pub speed: f32,
}
````

### Bevy-Specific Guidelines

#### Component Design

- Keep components small and focused
- Use marker components for tagging entities
- Prefer composition over inheritance

```rust
// Good: Small, focused components
#[derive(Component)]
pub struct Health(pub f32);

#[derive(Component)]
pub struct Player;

#[derive(Component)]
pub struct Velocity(pub Vec3);

// Avoid: Large, monolithic components
```

#### System Organization

- One system per file when systems are complex
- Group related systems in modules
- Use clear, descriptive system names

```rust
// File: src/systems/movement.rs
pub fn update_player_movement(
    time: Res<Time>,
    input: Res<ButtonInput<KeyCode>>,
    mut query: Query<&mut Transform, With<Player>>,
) {
    // Implementation
}
```

#### Resource Management

- Use resources for global state
- Prefer events for communication between systems
- Keep resources simple and focused

### Error Handling

- Use `Result<T, E>` for fallible operations
- Use `Option<T>` for nullable values
- Prefer explicit error types over `Box<dyn Error>`
- Log errors appropriately using the `log` crate

```rust
use log::{error, warn, info, debug};

fn load_asset(path: &str) -> Result<Asset, AssetError> {
    match std::fs::read(path) {
        Ok(data) => {
            info!("Successfully loaded asset: {}", path);
            Ok(Asset::from_bytes(data))
        }
        Err(e) => {
            error!("Failed to load asset {}: {}", path, e);
            Err(AssetError::IoError(e))
        }
    }
}
```

## 🧪 Testing Standards

### Unit Tests

- Write tests for all public functions
- Use descriptive test names
- Follow the Arrange-Act-Assert pattern

```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn player_movement_updates_position() {
        // Arrange
        let mut player = Player::new(Vec3::ZERO, 10.0);
        let delta_time = 1.0;
        let direction = Vec3::X;

        // Act
        player.move_in_direction(direction, delta_time);

        // Assert
        assert_eq!(player.position, Vec3::new(10.0, 0.0, 0.0));
    }
}
```

### Integration Tests

- Test system interactions
- Use Bevy's testing utilities
- Test complete workflows

### Performance Testing

- Use `cargo bench` for performance-critical code
- Profile with tools like `perf` or `cargo flamegraph`
- Monitor frame rates in development builds

## 🔄 Development Workflow

### Feature Development

1. **Create Feature Branch**

   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/my-feature
   ```

2. **Development Cycle**

   - Write failing tests first (TDD approach)
   - Implement functionality
   - Ensure all tests pass
   - Run `cargo fmt` and `cargo clippy`
   - Update documentation

3. **Code Review Checklist**

   - [ ] All tests pass
   - [ ] Code follows style guidelines
   - [ ] Documentation is updated
   - [ ] No compiler warnings
   - [ ] Performance impact considered

4. **Merge to Develop**

   ```bash
   git checkout develop
   git merge --no-ff feature/my-feature
   git push origin develop
   ```

### Quality Gates

Before merging any code:

1. **Automated Checks**

   ```bash
   # Format check
   cargo fmt --check

   # Linting
   cargo clippy -- -D warnings

   # Tests
   cargo test

   # Build check
   cargo build --release
   ```

2. **Manual Review**
   - Code readability and maintainability
   - Architecture consistency
   - Performance implications
   - Security considerations

## 🎮 Game Development Specific

### Asset Management

- Store assets in appropriate subdirectories under `assets/`
- Use consistent naming conventions
- Optimize assets for target platforms
- Document asset sources and licenses

### Performance Guidelines

- Target 60 FPS on development hardware
- Use Bevy's built-in profiling tools
- Monitor memory usage
- Optimize hot paths identified by profiling

### Prototype Integration

- Extract reusable components from prototypes
- Refactor prototype code to match project standards
- Document prototype origins in commit messages
- Preserve prototype functionality in feature branches

## 🚀 Release Process

### Pre-Release Checklist

- [ ] All tests pass
- [ ] Documentation is up to date
- [ ] Performance benchmarks meet targets
- [ ] Assets are optimized
- [ ] Version numbers are updated
- [ ] Changelog is updated

### Version Numbering

We use [Semantic Versioning](https://semver.org/):

- **MAJOR**: Incompatible API changes
- **MINOR**: New functionality (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Notes

- Document all user-facing changes
- Include migration guides for breaking changes
- Highlight performance improvements
- Credit contributors

## 🤝 Contributing

### Pull Request Process

1. Fork the repository
2. Create a feature branch from `develop`
3. Make your changes following the coding standards
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request to `develop`

### Code Review Guidelines

**For Authors: <AUTHORS>

- Keep PRs focused and reasonably sized
- Write clear commit messages
- Respond to feedback promptly
- Test your changes thoroughly

**For Reviewers:**

- Be constructive and specific in feedback
- Focus on code quality and maintainability
- Consider performance and security implications
- Approve only when confident in the changes

## 📚 Resources

- [Bevy Book](https://bevyengine.org/learn/book/)
- [Rust Book](https://doc.rust-lang.org/book/)
- [Bevy Examples](https://github.com/bevyengine/bevy/tree/main/examples)
- [Game Development Patterns](https://gameprogrammingpatterns.com/)

## 🆘 Getting Help

- Check existing documentation first
- Search through GitHub issues
- Ask questions in team chat
- Create detailed bug reports with reproduction steps
