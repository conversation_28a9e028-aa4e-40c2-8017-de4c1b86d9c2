[workspace]
members = [
    "prototypes/diorama",
    "prototypes/dual_mode",
]
resolver = "2"

[workspace.dependencies]
bevy = { version = "0.16.1", features = ["dynamic_linking"] }
rand = "0.9.2"
log = { version = "*", features = ["max_level_debug", "release_max_level_warn"] }

# # Local workspace dependencies for cross-prototype sharing
# bevy_lighting_toolkit = { path = "bevy_lighting_toolkit" }

[workspace.metadata.prototype-config]
# Configuration for prototype development workflow
# stable_toolkit_version = "0.1.0"
experimental_features = ["advanced_lighting", "shader_effects", "performance_optimizations"]

[package]
name = "fleeting"
version = "0.1.0"
edition = "2024"
description = "Fleeting game development workspace with prototyping tools"

[dependencies]
bevy = { workspace = true, features = ["dynamic_linking"] }
bevy_hanabi = "0.16"
# bevy_lighting_toolkit = { path = "bevy_lighting_toolkit" }
log = { workspace = true }
rand = { workspace = true }

# Enable a small amount of optimization in the dev profile.
[profile.dev]
opt-level = 1

# Enable a large amount of optimization in the dev profile for dependencies.
[profile.dev.package."*"]
opt-level = 3
