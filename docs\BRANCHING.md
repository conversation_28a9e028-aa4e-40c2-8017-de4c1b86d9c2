# Branching Strategy

This document provides a visual guide to our GitFlow branching strategy and merge/rebase guidelines.

## 🌳 Branch Hierarchy

```
main (production)
├── hotfix/critical-fix
└── release/v1.0.0
    └── develop (integration)
        ├── feature/player-movement
        ├── feature/lighting-system
        ├── feature/audio-manager
        └── feature/prototype-extraction
```

## 📊 GitFlow Diagram

```
main     ●─────●─────●─────●─────●─────●
         │     │     │     │     │     │
         │     │   hotfix  │     │     │
         │     │     ●─────┘     │     │
         │     │                 │     │
         │   release/v1.0        │   release/v2.0
         │     ●─────────────────┘     ●
         │     │                       │
develop  ●─────●─────●─────●─────●─────●─────●
         │           │     │     │           │
         │           │     │     │           │
feature  ●───────────┘     │     │           │
         │                 │     │           │
feature  ●─────────────────┘     │           │
         │                       │           │
feature  ●───────────────────────┘           │
         │                                   │
feature  ●───────────────────────────────────┘
```

## 🔄 Merge vs Rebase Guidelines

### When to Use Merge (--no-ff)

**Use for:**

- Feature branches → develop
- Release branches → main/develop
- Hotfix branches → main/develop

**Benefits:**

- Preserves branch history
- Shows feature completion points
- Easier to revert entire features

```bash
# Merge feature to develop
git checkout develop
git merge --no-ff feature/player-movement
```

### When to Use Rebase

**Use for:**

- Cleaning up feature branch history before merge
- Updating feature branch with latest develop
- Squashing related commits

**Benefits:**

- Linear, clean history
- Easier to follow commit progression
- Removes unnecessary merge commits

```bash
# Clean up feature branch before merge
git checkout feature/player-movement
git rebase -i develop

# Update feature with latest develop
git rebase develop
```

### When to Use Squash

**Use for:**

- Multiple small commits that belong together
- Work-in-progress commits
- Fixing typos or small adjustments

```bash
# Squash last 3 commits
git rebase -i HEAD~3
```

## 🎯 Branch-Specific Strategies

### Main Branch

- **Never** commit directly
- Only accepts merges from release/hotfix branches
- Always use `--no-ff` merges
- Tag all merges with version numbers

```bash
git checkout main
git merge --no-ff release/v1.0.0
git tag -a v1.0.0 -m "Release version 1.0.0"
```

### Develop Branch

- Accepts merges from feature branches
- Use `--no-ff` for feature merges
- Can use fast-forward for small fixes
- Regular integration point

```bash
# Feature merge (preserve history)
git merge --no-ff feature/lighting-system

# Small fix (fast-forward OK)
git merge fix/typo-in-readme
```

### Feature Branches

- Rebase regularly against develop
- Squash related commits before merge
- Keep focused on single feature
- Clean up history before merge request

```bash
# Regular update from develop
git rebase develop

# Interactive rebase to clean history
git rebase -i develop
```

### Release Branches

- Cherry-pick bug fixes only
- No new features
- Merge to both main and develop
- Use semantic versioning

```bash
# Bug fix during release
git cherry-pick <commit-hash>

# Merge to main
git checkout main
git merge --no-ff release/v1.0.0

# Merge back to develop
git checkout develop
git merge --no-ff release/v1.0.0
```

### Hotfix Branches

- Created from main
- Critical fixes only
- Merge to both main and develop
- Increment patch version

```bash
# Create hotfix from main
git checkout main
git checkout -b hotfix/critical-memory-leak

# After fix, merge to main
git checkout main
git merge --no-ff hotfix/critical-memory-leak
git tag -a v1.0.1 -m "Hotfix version 1.0.1"

# Merge to develop
git checkout develop
git merge --no-ff hotfix/critical-memory-leak
```

## 🧹 History Cleanup Strategies

### Interactive Rebase Commands

```bash
# Start interactive rebase
git rebase -i HEAD~5

# Common commands in rebase:
# pick   = use commit as-is
# reword = use commit, but edit message
# edit   = use commit, but stop for amending
# squash = use commit, but meld into previous commit
# fixup  = like squash, but discard commit message
# drop   = remove commit
```

### Example Cleanup Session

**Before cleanup:**

```
* 3a2b1c0 Fix typo in comment
* 4d5e6f7 Add player movement system
* 8g9h0i1 WIP: movement system
* 2j3k4l5 Add player component
* 6m7n8o9 Initial player setup
```

**Interactive rebase:**

```
pick 6m7n8o9 Initial player setup
squash 2j3k4l5 Add player component
squash 8g9h0i1 WIP: movement system
squash 4d5e6f7 Add player movement system
fixup 3a2b1c0 Fix typo in comment
```

**After cleanup:**

```
* 9p0q1r2 feat(player): implement complete movement system
```

## 🚦 Conflict Resolution

### Merge Conflicts

1. **Identify conflicted files**

   ```bash
   git status
   ```

2. **Resolve conflicts manually**

   - Edit files to resolve conflicts
   - Remove conflict markers (`<<<<<<<`, `=======`, `>>>>>>>`)
   - Test the resolution

3. **Complete the merge**

   ```bash
   git add <resolved-files>
   git commit
   ```

### Rebase Conflicts

1. **Resolve conflicts for each commit**

   ```bash
   # Edit conflicted files
   git add <resolved-files>
   git rebase --continue
   ```

2. **Skip problematic commits if needed**

   ```bash
   git rebase --skip
   ```

3. **Abort if necessary**
   ```bash
   git rebase --abort
   ```

## 📋 Pre-Merge Checklist

### Feature Branch Ready for Merge

- [ ] Branch is up to date with develop
- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Commit history is clean
- [ ] No merge conflicts
- [ ] Documentation updated
- [ ] Performance impact assessed

### Release Branch Ready for Merge

- [ ] All planned features included
- [ ] Version numbers updated
- [ ] Changelog updated
- [ ] All tests pass
- [ ] Performance benchmarks met
- [ ] Documentation complete
- [ ] Release notes prepared

## 🎨 Commit Message Art

### Good Commit History

```
* feat(player): add movement controls with WASD keys
* feat(camera): implement smooth follow camera
* fix(physics): resolve collision detection edge case
* docs(readme): update installation instructions
* refactor(systems): reorganize ECS system modules
```

### Poor Commit History (to avoid)

```
* WIP
* fix stuff
* more changes
* oops
* final version
* actually final
```

## 🔧 Git Configuration for Team

### Recommended Global Settings

```bash
# Better merge conflict resolution
git config --global merge.conflictstyle diff3

# Automatic rebase for pulls
git config --global pull.rebase true

# Better log format
git config --global alias.lg "log --oneline --graph --decorate --all"

# Useful aliases
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.unstage "reset HEAD --"
```

### Team Hooks (Optional)

- Pre-commit: Run tests and linting
- Pre-push: Ensure no direct pushes to main
- Commit-msg: Validate commit message format

## 📈 Metrics and Monitoring

### Branch Health Indicators

- **Feature branch age**: Should be < 2 weeks
- **Develop stability**: Should always build and pass tests
- **Main protection**: Zero direct commits
- **Release frequency**: Regular, predictable releases

### Code Quality Metrics

- Test coverage percentage
- Build success rate
- Time from feature start to merge
- Number of hotfixes per release

## 🆘 Emergency Procedures

### Broken Main Branch

1. Identify the breaking commit
2. Create hotfix branch from last known good commit
3. Fix the issue
4. Fast-track through review process
5. Deploy immediately

### Lost Work Recovery

```bash
# Find lost commits
git reflog

# Recover lost branch
git checkout -b recovered-branch <commit-hash>

# Cherry-pick specific commits
git cherry-pick <commit-hash>
```

### Accidental Force Push

1. Check if anyone else pulled the changes
2. Use `git reflog` to find previous state
3. Force push the correct state (if safe)
4. Communicate with team immediately
