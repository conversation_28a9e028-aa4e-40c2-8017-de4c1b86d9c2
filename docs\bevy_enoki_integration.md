# Bevy Enoki Particle System Integration

## Overview

This document describes the integration of bevy_enoki, a 2D particle system, into the fleeting game project. The integration provides dynamic visual effects including engine exhaust, explosions, and environmental effects.

## Features Added

### 1. Particle System Plugin
- **ParticleSystemPlugin**: Main plugin that integrates bevy_enoki with our game
- **Custom Materials**: Three specialized materials for different effects
- **Hot-reloadable Configuration**: Particle effects defined in `.ron` files

### 2. Engine Exhaust System
- **EngineExhaust Component**: Marks entities that should emit exhaust particles
- **Dynamic Activation**: Exhaust activates when the player moves
- **Intensity Control**: Exhaust intensity varies with movement speed
- **Custom Shader**: `engine_exhaust.wgsl` creates heat distortion and flame effects

### 3. Explosion Effects
- **ExplosionEffect Component**: Configurable explosion parameters
- **Multi-phase Animation**: Flash → Fire → Smoke progression
- **Custom Shader**: `explosion.wgsl` with turbulent motion and color transitions
- **One-shot System**: Explosions automatically clean up after completion

### 4. Starfield Background
- **StarfieldEffect Component**: Ambient space environment
- **Twinkling Animation**: Stars pulse and shimmer realistically
- **Custom Shader**: `starfield.wgsl` with color variation and diffraction spikes
- **Low Performance Impact**: Optimized for background ambience

## Technical Implementation

### Custom Materials

#### EngineExhaustMaterial
```rust
#[derive(AsBindGroup, Asset, TypePath, Clone, Default)]
pub struct EngineExhaustMaterial {
    #[texture(0)] #[sampler(1)]
    pub texture: Option<Handle<Image>>,
    #[uniform(2)] pub intensity: f32,
    #[uniform(3)] pub time: f32,
}
```

#### ExplosionMaterial
```rust
#[derive(AsBindGroup, Asset, TypePath, Clone, Default)]
pub struct ExplosionMaterial {
    #[texture(0)] #[sampler(1)]
    pub texture: Option<Handle<Image>>,
    #[uniform(2)] pub explosion_time: f32,
    #[uniform(3)] pub intensity: f32,
}
```

#### StarfieldMaterial
```rust
#[derive(AsBindGroup, Asset, TypePath, Clone, Default)]
pub struct StarfieldMaterial {
    #[texture(0)] #[sampler(1)]
    pub texture: Option<Handle<Image>>,
    #[uniform(2)] pub twinkle_speed: f32,
    #[uniform(3)] pub brightness: f32,
}
```

### Particle Configuration Files

#### Engine Exhaust (`assets/particles/engine_exhaust.particle.ron`)
- **Spawn Rate**: 50 particles/second
- **Emission Shape**: Small circle behind ship
- **Movement**: Backwards with deceleration
- **Color Curve**: Blue to white heat gradient
- **Scale Curve**: Grow then shrink for realistic exhaust

#### Explosion (`assets/particles/explosion.particle.ron`)
- **Spawn Rate**: 200 particles/second burst
- **Emission Shape**: Large circular explosion
- **Movement**: High-speed radial expansion
- **Color Curve**: White flash → Orange fire → Dark smoke
- **Scale Curve**: Rapid expansion then shrinkage

#### Starfield (`assets/particles/starfield.particle.ron`)
- **Spawn Rate**: 5 particles/second
- **Emission Shape**: Large background area
- **Movement**: Slow gentle drift
- **Color Curve**: Subtle twinkling variations
- **Long Lifetime**: Persistent background effect

### Shader Features

#### Engine Exhaust Shader
- **Heat Distortion**: Fractal noise-based UV distortion
- **Flame Gradient**: Hotter colors at center
- **Animated Flicker**: Time-based intensity variation
- **Color Mixing**: Blue (cool) to white (hot) gradient

#### Explosion Shader
- **Turbulent Motion**: Fractal Brownian Motion for realistic fire/smoke
- **Phase System**: Flash → Fire → Smoke transitions
- **Sparkle Effects**: Random debris particles
- **Radial Falloff**: Proper edge fading

#### Starfield Shader
- **Multi-frequency Twinkling**: Complex shimmer patterns
- **Color Variations**: Warm/cool star types
- **Diffraction Spikes**: Cross-shaped light spikes for bright stars
- **Smooth Falloff**: Soft star edges

## Integration with Player System

### Player Movement Integration
The Player input system now updates the EngineExhaust component:
```rust
// Update engine exhaust based on movement
if let Some(mut exhaust) = engine_exhaust {
    exhaust.active = movement.length() > 0.0;
    exhaust.intensity = movement.length().clamp(0.0, 1.0);
}
```

### Automatic Particle Spawning
The particle system automatically detects entities with EngineExhaust components and spawns appropriate particle systems.

## Performance Considerations

### CPU-based Simulation
- bevy_enoki uses CPU calculation with GPU instancing
- Works well on WASM and mobile platforms
- Efficient for moderate particle counts

### Optimization Features
- **Automatic Cleanup**: Finished effects are automatically removed
- **LOD System**: Particle density can be adjusted based on performance
- **Hot Reloading**: Effect parameters can be tuned without recompilation

## Usage Examples

### Adding Engine Exhaust to an Entity
```rust
commands.spawn((
    // ... other components
    EngineExhaust::default(),
));
```

### Creating an Explosion Effect
```rust
create_explosion_effect(
    &mut commands,
    &mut explosion_materials,
    &asset_server,
    explosion_position,
    explosion_scale,
);
```

### Adding Background Starfield
```rust
create_starfield_effect(
    &mut commands,
    &mut starfield_materials,
    &asset_server,
    background_position,
);
```

## Future Enhancements

### Planned Features
1. **Weapon Effects**: Muzzle flashes and projectile trails
2. **Environmental Effects**: Space dust and nebula particles
3. **Damage Effects**: Sparks and debris from damaged ships
4. **Warp Effects**: Particle trails for faster-than-light travel

### Performance Optimizations
1. **GPU Compute Shaders**: For high particle count effects
2. **Particle Pooling**: Reuse particle instances for better memory usage
3. **Culling System**: Skip particles outside camera view
4. **Quality Settings**: Adjustable particle density for different hardware

## Dependencies

- **bevy_enoki v0.4.0**: Main particle system
- **Compatible with Bevy 0.16**: Latest Bevy version support
- **WASM Compatible**: Works in web browsers
- **Mobile Optimized**: Efficient on mobile devices

## File Structure

```
src/components/particle_system.rs    # Main particle system code
assets/particles/                    # Particle effect configurations
├── engine_exhaust.particle.ron
├── explosion.particle.ron
└── starfield.particle.ron
assets/shaders/                      # Custom particle shaders
├── engine_exhaust.wgsl
├── explosion.wgsl
└── starfield.wgsl
docs/bevy_enoki_integration.md       # This documentation
```

This integration provides a solid foundation for visual effects in the fleeting game while maintaining good performance and extensibility for future enhancements.
