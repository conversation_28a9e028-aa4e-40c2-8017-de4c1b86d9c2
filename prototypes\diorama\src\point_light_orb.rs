use bevy::prelude::*;

/// Movement patterns for PointLightOrbs
#[derive(<PERSON><PERSON>, Debug)]
pub enum MovementPattern {
    /// No movement
    Static,
    /// Circular orbit around a center point
    Orbit { center: Vec3, radius: f32, speed: f32 },
    /// Figure-8 pattern
    Figure8 { center: Vec3, scale: f32, speed: f32 },
    /// Linear back-and-forth movement
    Linear { start: Vec3, end: Vec3, speed: f32 },
    /// Random floating movement
    Float { center: Vec3, range: f32, speed: f32 },
}

/// A synchronized point light + visible orb entity
/// This ensures the light and visual representation always stay in sync
#[derive(Component, Clone)]
pub struct PointLightOrb {
    /// Base color of the orb and light
    pub color: Color,
    /// Light intensity
    pub intensity: f32,
    /// Base intensity (for animations/flickering)
    pub base_intensity: f32,
    /// Orb radius
    pub radius: f32,
    /// Whether the orb should flicker/animate
    pub animate: bool,
    /// Animation speed multiplier
    pub animation_speed: f32,
    /// Light range
    pub range: f32,
    /// Movement pattern
    pub movement: MovementPattern,
    /// Internal timer for movement calculations
    pub movement_timer: f32,
}

impl Default for PointLightOrb {
    fn default() -> Self {
        Self {
            color: Color::WHITE,
            intensity: 800.0,  // Reduced intensity
            base_intensity: 800.0,
            radius: 0.08,      // Smaller radius
            animate: true,
            animation_speed: 1.0,
            range: 8.0,        // Much smaller range for better performance
            movement: MovementPattern::Static,
            movement_timer: 0.0,
        }
    }
}

/// Marker component for the visual orb part
#[derive(Component)]
pub struct OrbVisual;

/// Marker component for the light part
#[derive(Component)]
pub struct OrbLight;

/// Bundle for spawning a complete PointLightOrb
#[derive(Bundle)]
pub struct PointLightOrbBundle {
    pub orb: PointLightOrb,
    pub transform: Transform,
    pub global_transform: GlobalTransform,
    pub visibility: Visibility,
    pub inherited_visibility: InheritedVisibility,
    pub view_visibility: ViewVisibility,
}

impl Default for PointLightOrbBundle {
    fn default() -> Self {
        Self {
            orb: PointLightOrb::default(),
            transform: Transform::default(),
            global_transform: GlobalTransform::default(),
            visibility: Visibility::default(),
            inherited_visibility: InheritedVisibility::default(),
            view_visibility: ViewVisibility::default(),
        }
    }
}

/// System to spawn the visual and light components for PointLightOrbs
pub fn spawn_point_light_orb_components(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    orb_query: Query<(Entity, &PointLightOrb, &Transform), (Added<PointLightOrb>, Without<OrbVisual>)>,
) {
    for (entity, orb, transform) in orb_query.iter() {
        // Create the visual orb (soft, glowing sphere)
        let orb_visual = commands.spawn((
            Mesh3d(meshes.add(Sphere::new(orb.radius))),
            MeshMaterial3d(materials.add(StandardMaterial {
                base_color: Color::LinearRgba(LinearRgba::from(orb.color).with_alpha(0.7)), // Semi-transparent
                emissive: LinearRgba::from(orb.color) * (orb.intensity / 1000.0),
                unlit: true, // Don't let other lights affect it
                alpha_mode: AlphaMode::Blend,
                cull_mode: None, // Render both sides for better glow effect
                ..default()
            })),
            Transform::from_translation(Vec3::ZERO), // Relative to parent
            OrbVisual,
        )).id();

        // Create the point light (optimized for performance)
        let orb_light = commands.spawn((
            PointLight {
                color: orb.color,
                intensity: orb.intensity,
                range: orb.range,
                radius: orb.radius,
                shadows_enabled: false, // Disable shadows for better performance
                ..default()
            },
            Transform::from_translation(Vec3::ZERO), // Relative to parent
            OrbLight,
        )).id();

        // Make both children of the main orb entity
        commands.entity(entity).add_children(&[orb_visual, orb_light]);

        println!("🔮 Spawned PointLightOrb at {:?} with color {:?}, intensity {}", 
                 transform.translation, orb.color, orb.intensity);
    }
}

/// System to update PointLightOrb movement patterns
pub fn update_point_light_orb_movement(
    time: Res<Time>,
    mut orb_query: Query<(&mut PointLightOrb, &mut Transform)>,
) {
    let delta_time = time.delta_secs();

    for (mut orb, mut transform) in orb_query.iter_mut() {
        orb.movement_timer += delta_time;

        let new_position = match &orb.movement {
            MovementPattern::Static => transform.translation,

            MovementPattern::Orbit { center, radius, speed } => {
                let angle = orb.movement_timer * speed;
                *center + Vec3::new(
                    radius * angle.cos(),
                    radius * (angle * 0.7).sin(), // Different frequency for Y to make it more interesting
                    radius * angle.sin(),
                )
            },

            MovementPattern::Figure8 { center, scale, speed } => {
                let t = orb.movement_timer * speed;
                *center + Vec3::new(
                    scale * (t * 2.0).sin(),
                    scale * t.sin(),
                    scale * (t * 1.5).cos(),
                )
            },

            MovementPattern::Linear { start, end, speed } => {
                let t = ((orb.movement_timer * speed).sin() + 1.0) * 0.5; // Oscillate between 0 and 1
                start.lerp(*end, t)
            },

            MovementPattern::Float { center, range, speed } => {
                let t = orb.movement_timer * speed;
                *center + Vec3::new(
                    range * (t * 1.3).sin() * 0.5,
                    range * (t * 0.8).cos() * 0.3,
                    range * (t * 1.7).sin() * 0.4,
                )
            },
        };

        transform.translation = new_position;
    }
}

/// System to update PointLightOrb animations and keep visual/light in sync
pub fn update_point_light_orbs(
    time: Res<Time>,
    mut orb_query: Query<(&mut PointLightOrb, &Children)>,
    mut visual_query: Query<&mut MeshMaterial3d<StandardMaterial>, (With<OrbVisual>, Without<OrbLight>)>,
    mut light_query: Query<&mut PointLight, (With<OrbLight>, Without<OrbVisual>)>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    let elapsed = time.elapsed_secs();

    for (mut orb, children) in orb_query.iter_mut() {
        // Only update if animation is enabled
        if !orb.animate {
            continue;
        }

        // Calculate current intensity (with animation if enabled)
        let flicker = (elapsed * orb.animation_speed * 2.0).sin() * 0.15 + 0.85; // Reduced flicker intensity
        let current_intensity = orb.base_intensity * flicker;

        // Only update if intensity changed significantly (reduce unnecessary updates)
        if (current_intensity - orb.intensity).abs() < 50.0 {
            continue;
        }

        orb.intensity = current_intensity;

        // Update visual and light components
        for child in children.iter() {
            // Update point light (more important for lighting)
            if let Ok(mut light) = light_query.get_mut(child) {
                light.intensity = current_intensity;
            }

            // Update visual orb less frequently for performance
            if elapsed.fract() < 0.1 { // Only update visual ~10% of frames
                if let Ok(material_handle) = visual_query.get_mut(child) {
                    if let Some(material) = materials.get_mut(&material_handle.0) {
                        material.emissive = LinearRgba::from(orb.color) * (current_intensity / 1000.0);
                    }
                }
            }
        }
    }
}

/// Helper function to create a PointLightOrb with custom settings
pub fn create_point_light_orb(
    color: Color,
    intensity: f32,
    radius: f32,
    position: Vec3,
    animate: bool,
) -> PointLightOrbBundle {
    PointLightOrbBundle {
        orb: PointLightOrb {
            color,
            intensity,
            base_intensity: intensity,
            radius,
            animate,
            animation_speed: 1.0,
            range: 8.0,
            movement: MovementPattern::Static,
            movement_timer: 0.0,
        },
        transform: Transform::from_translation(position),
        ..default()
    }
}

/// Helper function to create a PointLightOrb with movement pattern
pub fn create_moving_point_light_orb(
    color: Color,
    intensity: f32,
    radius: f32,
    position: Vec3,
    animate: bool,
    movement: MovementPattern,
) -> PointLightOrbBundle {
    PointLightOrbBundle {
        orb: PointLightOrb {
            color,
            intensity,
            base_intensity: intensity,
            radius,
            animate,
            animation_speed: 1.0,
            range: 8.0,
            movement,
            movement_timer: 0.0,
        },
        transform: Transform::from_translation(position),
        ..default()
    }
}

/// Plugin to add PointLightOrb functionality
pub struct PointLightOrbPlugin;

impl Plugin for PointLightOrbPlugin {
    fn build(&self, app: &mut App) {
        app.add_systems(Update, (
            spawn_point_light_orb_components,
            update_point_light_orb_movement,
            update_point_light_orbs,
        ).chain()); // Chain to ensure movement happens before visual updates
    }
}
