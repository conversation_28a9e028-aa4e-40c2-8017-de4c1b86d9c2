// Engine Exhaust Particle Shader
// Creates animated exhaust particles with heat distortion and glow effects

#import bevy_enoki::particle_vertex_out::{ VertexOutput }

@group(1) @binding(0) var texture: texture_2d<f32>;
@group(1) @binding(1) var texture_sampler: sampler;

struct ExhaustMaterial {
    intensity: f32,
    time: f32,
}

@group(1) @binding(2) var<uniform> material: ExhaustMaterial;

// Noise function for heat distortion
fn noise(p: vec2<f32>) -> f32 {
    let K1 = vec2<f32>(23.14069263277926, 2.665144142690225);
    return fract(cos(dot(p, K1)) * 12345.6789);
}

// Fractal noise for more complex patterns
fn fbm(p: vec2<f32>) -> f32 {
    var value = 0.0;
    var amplitude = 0.5;
    var frequency = 1.0;
    
    for (var i = 0; i < 4; i++) {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

@fragment
fn fragment(in: VertexOutput) -> @location(0) vec4<f32> {
    let uv = in.uv;
    let time = material.time;
    let intensity = material.intensity;
    
    // Sample base texture
    var base_color = textureSample(texture, texture_sampler, uv);
    
    // Create heat distortion effect
    let distortion_uv = uv + vec2<f32>(
        fbm(uv * 3.0 + vec2<f32>(time * 2.0, 0.0)) * 0.1,
        fbm(uv * 2.0 + vec2<f32>(0.0, time * 3.0)) * 0.15
    );
    
    // Sample with distortion
    let distorted_color = textureSample(texture, texture_sampler, distortion_uv);
    
    // Create exhaust flame gradient (hotter at center)
    let center_dist = length(uv - vec2<f32>(0.5, 0.5));
    let flame_gradient = 1.0 - smoothstep(0.0, 0.5, center_dist);
    
    // Animate flame intensity
    let flame_flicker = 0.8 + 0.2 * sin(time * 8.0 + uv.x * 10.0);
    let animated_intensity = intensity * flame_gradient * flame_flicker;
    
    // Create color gradient from blue (cool) to white (hot)
    let heat_factor = animated_intensity * in.lifetime_frac;
    var exhaust_color = mix(
        vec3<f32>(0.2, 0.4, 1.0), // Cool blue
        vec3<f32>(0.8, 0.9, 1.0), // Hot white
        heat_factor
    );
    
    // Add some orange/yellow for variety
    if (heat_factor > 0.7) {
        exhaust_color = mix(exhaust_color, vec3<f32>(1.0, 0.8, 0.4), (heat_factor - 0.7) * 3.0);
    }
    
    // Combine base texture with procedural effects
    let final_color = mix(base_color.rgb, exhaust_color, 0.7);
    
    // Calculate final alpha with fade based on lifetime
    let lifetime_fade = 1.0 - in.lifetime_frac;
    let alpha = base_color.a * in.color.a * animated_intensity * lifetime_fade;
    
    return vec4<f32>(final_color * intensity, alpha);
}
