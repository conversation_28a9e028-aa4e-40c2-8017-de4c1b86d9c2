use bevy::prelude::*;
use bevy_hanabi::prelude::*;

/// Extensible particle effect types for different game scenarios
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub enum ParticleEffectType {
    EngineExhaust,
    Starfield,
    Explosion,
    Sparks,
    Smoke,
    Fire,
    Magic,
    Debris,
}

/// Configuration for particle effects with extensible parameters
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct ParticleEffectConfig {
    pub effect_type: ParticleEffectType,
    pub spawn_rate: f32,
    pub spawn_count: u32,
    pub lifetime: f32,
    pub speed: f32,
    pub scale: f32,
    pub color: LinearRgba,
    pub gravity: Vec3,
    pub emission_radius: f32,
    pub one_shot: bool,
}

impl ParticleEffectConfig {
    /// Create engine exhaust effect configuration
    pub fn engine_exhaust() -> Self {
        Self {
            effect_type: ParticleEffectType::EngineExhaust,
            spawn_rate: 50.0,  // particles per second
            spawn_count: 5,
            lifetime: 2.0,
            speed: 100.0,
            scale: 0.5,
            color: LinearRgba::new(0.4, 0.7, 1.0, 0.8),
            gravity: Vec3::new(0.0, -50.0, 0.0),
            emission_radius: 0.2,
            one_shot: false,
        }
    }

    /// Create starfield background effect configuration
    pub fn starfield() -> Self {
        Self {
            effect_type: ParticleEffectType::Starfield,
            spawn_rate: 20.0,  // particles per second
            spawn_count: 3,
            lifetime: 15.0,
            speed: 30.0,
            scale: 0.3,
            color: LinearRgba::new(1.0, 1.0, 1.0, 0.8),
            gravity: Vec3::new(0.0, -10.0, 0.0),
            emission_radius: 10.0,
            one_shot: false,
        }
    }

    /// Create explosion effect configuration
    pub fn explosion() -> Self {
        Self {
            effect_type: ParticleEffectType::Explosion,
            spawn_rate: 1000.0,  // High burst rate
            spawn_count: 100,
            lifetime: 3.0,
            speed: 200.0,
            scale: 1.0,
            color: LinearRgba::new(1.0, 0.5, 0.1, 1.0),
            gravity: Vec3::new(0.0, -100.0, 0.0),
            emission_radius: 0.5,
            one_shot: true,
        }
    }

    /// Create sparks effect configuration
    pub fn sparks() -> Self {
        Self {
            effect_type: ParticleEffectType::Sparks,
            spawn_rate: 200.0,
            spawn_count: 20,
            lifetime: 1.5,
            speed: 150.0,
            scale: 0.2,
            color: LinearRgba::new(1.0, 1.0, 0.3, 1.0),
            gravity: Vec3::new(0.0, -200.0, 0.0),
            emission_radius: 0.1,
            one_shot: true,
        }
    }
}

/// Component to request engine exhaust particles
#[derive(Component, Default)]
pub struct EngineExhaust {
    pub intensity: f32,
    pub active: bool,
    pub spawn_offset: Vec3,
}

/// Component for explosion effects
#[derive(Component)]
pub struct ExplosionEffect {
    pub scale: f32,
    pub duration: f32,
}

/// Component for starfield background
#[derive(Component)]
pub struct StarfieldEffect {
    pub density: f32,
    pub speed: f32,
}

/// Component that triggers explosion particles when entity is destroyed
#[derive(Component)]
pub struct ExplodesOnDestroy {
    pub scale: f32,
}

/// Component that triggers spark particles when entity is hit
#[derive(Component)]
pub struct SparksOnHit {
    pub intensity: f32,
}

/// Resource to store particle effect assets
#[derive(Resource)]
pub struct ParticleEffects {
    pub engine_exhaust: Handle<EffectAsset>,
    pub starfield: Handle<EffectAsset>,
    pub explosion: Handle<EffectAsset>,
    pub sparks: Handle<EffectAsset>,
}

/// Plugin for the Particle System
pub struct ParticleSystemPlugin;

impl Plugin for ParticleSystemPlugin {
    fn build(&self, app: &mut App) {
        app.add_systems(Startup, setup_particle_effects)
            .add_systems(Update, (
                handle_engine_exhaust_requests,
                debug_particle_count,
            ));
    }
}

/// Create particle effect assets
fn setup_particle_effects(
    mut commands: Commands,
    mut effects: ResMut<Assets<EffectAsset>>,
) {
    info!("Setting up particle effects...");

    // Create engine exhaust effect
    let engine_exhaust_effect = create_engine_exhaust_effect();
    let engine_exhaust_handle = effects.add(engine_exhaust_effect);

    // Create starfield effect
    let starfield_effect = create_starfield_effect_asset();
    let starfield_handle = effects.add(starfield_effect);

    // Create explosion effect
    let explosion_effect = create_explosion_effect();
    let explosion_handle = effects.add(explosion_effect);

    // Create sparks effect
    let sparks_effect = create_sparks_effect();
    let sparks_handle = effects.add(sparks_effect);

    // Store handles as resource
    commands.insert_resource(ParticleEffects {
        engine_exhaust: engine_exhaust_handle,
        starfield: starfield_handle,
        explosion: explosion_handle,
        sparks: sparks_handle,
    });

    info!("Particle effects setup complete");
}

/// Create engine exhaust effect asset
fn create_engine_exhaust_effect() -> EffectAsset {
    let config = ParticleEffectConfig::engine_exhaust();
    
    // Create a color gradient from blue to transparent
    let mut gradient = Gradient::new();
    gradient.add_key(0.0, config.color.to_vec4());
    gradient.add_key(1.0, Vec4::new(config.color.red, config.color.green, config.color.blue, 0.0));

    // Create expression module
    let mut module = Module::default();

    // Initialize position with slight randomness
    let init_pos = SetPositionSphereModifier {
        center: module.lit(Vec3::ZERO),
        radius: module.lit(config.emission_radius),
        dimension: ShapeDimension::Volume,
    };

    // Initialize velocity pointing backward (engine exhaust)
    let init_vel = SetVelocitySphereModifier {
        center: module.lit(Vec3::ZERO),
        speed: module.lit(config.speed),
    };

    // Set lifetime
    let init_lifetime = SetAttributeModifier::new(
        Attribute::LIFETIME,
        module.lit(config.lifetime),
    );

    // Apply gravity
    let update_accel = AccelModifier::new(module.lit(config.gravity));

    EffectAsset::new(
        32768, // max particles
        SpawnerSettings::rate(config.spawn_rate.into()),
        module,
    )
    .with_name("EngineExhaust")
    .init(init_pos)
    .init(init_vel)
    .init(init_lifetime)
    .update(update_accel)
    .render(ColorOverLifetimeModifier { gradient, ..default() })
}

/// Create starfield effect asset
fn create_starfield_effect_asset() -> EffectAsset {
    let config = ParticleEffectConfig::starfield();
    
    // Create a simple white color
    let mut gradient = Gradient::new();
    gradient.add_key(0.0, config.color.to_vec4());
    gradient.add_key(1.0, config.color.to_vec4());

    let mut module = Module::default();

    // Initialize position over a large area
    let init_pos = SetPositionSphereModifier {
        center: module.lit(Vec3::ZERO),
        radius: module.lit(config.emission_radius),
        dimension: ShapeDimension::Volume,
    };

    // Initialize downward velocity
    let init_vel = SetVelocitySphereModifier {
        center: module.lit(Vec3::ZERO),
        speed: module.lit(config.speed),
    };

    let init_lifetime = SetAttributeModifier::new(
        Attribute::LIFETIME,
        module.lit(config.lifetime),
    );

    let update_accel = AccelModifier::new(module.lit(config.gravity));

    EffectAsset::new(
        65536, // max particles for starfield
        SpawnerSettings::rate(config.spawn_rate.into()),
        module,
    )
    .with_name("Starfield")
    .init(init_pos)
    .init(init_vel)
    .init(init_lifetime)
    .update(update_accel)
    .render(ColorOverLifetimeModifier { gradient, ..default() })
}

/// Create explosion effect asset
fn create_explosion_effect() -> EffectAsset {
    let config = ParticleEffectConfig::explosion();
    
    let mut gradient = Gradient::new();
    gradient.add_key(0.0, config.color.to_vec4());
    gradient.add_key(0.5, Vec4::new(1.0, 0.8, 0.2, 0.8));
    gradient.add_key(1.0, Vec4::new(0.2, 0.2, 0.2, 0.0));

    let mut module = Module::default();

    let init_pos = SetPositionSphereModifier {
        center: module.lit(Vec3::ZERO),
        radius: module.lit(config.emission_radius),
        dimension: ShapeDimension::Volume,
    };

    let init_vel = SetVelocitySphereModifier {
        center: module.lit(Vec3::ZERO),
        speed: module.lit(config.speed),
    };

    let init_lifetime = SetAttributeModifier::new(
        Attribute::LIFETIME,
        module.lit(config.lifetime),
    );

    let update_accel = AccelModifier::new(module.lit(config.gravity));

    EffectAsset::new(
        16384,
        SpawnerSettings::burst((config.spawn_count as f32).into(), 0.1.into()),
        module,
    )
    .with_name("Explosion")
    .init(init_pos)
    .init(init_vel)
    .init(init_lifetime)
    .update(update_accel)
    .render(ColorOverLifetimeModifier { gradient, ..default() })
}

/// Create sparks effect asset
fn create_sparks_effect() -> EffectAsset {
    let config = ParticleEffectConfig::sparks();
    
    let mut gradient = Gradient::new();
    gradient.add_key(0.0, config.color.to_vec4());
    gradient.add_key(1.0, Vec4::new(config.color.red, config.color.green, config.color.blue, 0.0));

    let mut module = Module::default();

    let init_pos = SetPositionSphereModifier {
        center: module.lit(Vec3::ZERO),
        radius: module.lit(config.emission_radius),
        dimension: ShapeDimension::Surface,
    };

    let init_vel = SetVelocitySphereModifier {
        center: module.lit(Vec3::ZERO),
        speed: module.lit(config.speed),
    };

    let init_lifetime = SetAttributeModifier::new(
        Attribute::LIFETIME,
        module.lit(config.lifetime),
    );

    let update_accel = AccelModifier::new(module.lit(config.gravity));

    EffectAsset::new(
        8192,
        SpawnerSettings::burst((config.spawn_count as f32).into(), 0.05.into()),
        module,
    )
    .with_name("Sparks")
    .init(init_pos)
    .init(init_vel)
    .init(init_lifetime)
    .update(update_accel)
    .render(ColorOverLifetimeModifier { gradient, ..default() })
}

/// Handle engine exhaust particle requests
fn handle_engine_exhaust_requests(
    mut commands: Commands,
    particle_effects: Res<ParticleEffects>,
    query: Query<(Entity, &Transform), (With<EngineExhaust>, Without<ParticleEffect>)>,
) {
    for (entity, transform) in query.iter() {
        info!("Adding engine exhaust particle effect to entity {:?}", entity);

        // Add particle effect to the entity
        commands.entity(entity).insert(ParticleEffect::new(particle_effects.engine_exhaust.clone()));

        info!("Spawned EngineExhaust particle effect at {:?}", transform.translation);
    }
}

/// Debug system to show particle counts
fn debug_particle_count(
    time: Res<Time>,
    mut last_debug: Local<f32>,
    spawners: Query<&ParticleEffect>,
) {
    let current_time = time.elapsed_secs();
    if current_time - *last_debug > 2.0 {
        let spawner_count = spawners.iter().count();
        info!("Particle Debug: {} spawners active", spawner_count);
        *last_debug = current_time;
    }
}

/// Create starfield effect at a specific position
pub fn create_starfield_effect(
    commands: &mut Commands,
    particle_effects: &ParticleEffects,
    position: Vec3,
) {
    info!("Spawned Starfield particle effect at {:?}", position);

    commands.spawn((
        ParticleEffect::new(particle_effects.starfield.clone()),
        Transform::from_translation(position),
        StarfieldEffect {
            density: 1.0,
            speed: 1.0,
        },
    ));
}

/// Spawn explosion effect at position
pub fn spawn_explosion(
    commands: &mut Commands,
    particle_effects: &ParticleEffects,
    position: Vec3,
) {
    info!("Spawning explosion at {:?}", position);

    commands.spawn((
        ParticleEffect::new(particle_effects.explosion.clone()),
        Transform::from_translation(position),
        ExplosionEffect {
            scale: 1.0,
            duration: 3.0,
        },
    ));
}

/// Spawn sparks effect at position
pub fn spawn_sparks(
    commands: &mut Commands,
    particle_effects: &ParticleEffects,
    position: Vec3,
) {
    info!("Spawning sparks at {:?}", position);

    commands.spawn((
        ParticleEffect::new(particle_effects.sparks.clone()),
        Transform::from_translation(position),
    ));
}
