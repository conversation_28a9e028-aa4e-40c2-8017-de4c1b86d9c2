// Starfield Particle Shader
// Creates twinkling stars with subtle color variation

#import bevy_enoki::particle_vertex_out::{ VertexOutput }

@group(1) @binding(0) var texture: texture_2d<f32>;
@group(1) @binding(1) var texture_sampler: sampler;

struct StarfieldMaterial {
    twinkle_speed: f32,
    brightness: f32,
}

@group(1) @binding(2) var<uniform> material: StarfieldMaterial;

// Simple hash function
fn hash(p: f32) -> f32 {
    return fract(sin(p * 127.1) * 43758.5453123);
}

// Smooth step function for twinkling
fn smoothTwinkle(t: f32) -> f32 {
    return 0.5 + 0.5 * sin(t);
}

@fragment
fn fragment(in: VertexOutput) -> @location(0) vec4<f32> {
    let uv = in.uv;
    let time = in.lifetime_total * material.twinkle_speed;
    let brightness = material.brightness;
    
    // Sample base texture
    let base_color = textureSample(texture, texture_sampler, uv);
    
    // Distance from center for star shape
    let center_dist = length(uv - vec2<f32>(0.5, 0.5));
    
    // Create star shape with soft edges
    let star_shape = 1.0 - smoothstep(0.0, 0.5, center_dist);
    
    // Create twinkling effect with multiple frequencies
    let twinkle_seed = hash(in.lifetime_total * 1000.0); // Unique per particle
    let twinkle1 = smoothTwinkle(time * 2.0 + twinkle_seed * 6.28);
    let twinkle2 = smoothTwinkle(time * 3.0 + twinkle_seed * 12.56);
    let twinkle3 = smoothTwinkle(time * 1.5 + twinkle_seed * 18.84);
    
    let combined_twinkle = (twinkle1 + twinkle2 * 0.5 + twinkle3 * 0.3) / 1.8;
    
    // Vary star color slightly based on particle properties
    let color_variation = hash(twinkle_seed);
    var star_color = vec3<f32>(0.9, 0.95, 1.0); // Base white-blue
    
    // Add slight color variations
    if (color_variation < 0.3) {
        star_color = vec3<f32>(1.0, 0.9, 0.8); // Warm white
    } else if (color_variation < 0.6) {
        star_color = vec3<f32>(0.8, 0.9, 1.0); // Cool blue
    } else if (color_variation < 0.8) {
        star_color = vec3<f32>(1.0, 0.95, 0.9); // Slightly warm
    }
    
    // Apply twinkling to brightness
    let twinkle_brightness = brightness * (0.3 + 0.7 * combined_twinkle);
    
    // Create subtle cross-shaped diffraction spikes for brighter stars
    let spike_h = exp(-abs(uv.y - 0.5) * 20.0) * 0.1;
    let spike_v = exp(-abs(uv.x - 0.5) * 20.0) * 0.1;
    let spikes = (spike_h + spike_v) * twinkle_brightness * star_shape;
    
    // Combine all effects
    let final_intensity = (star_shape + spikes) * twinkle_brightness;
    let final_color = star_color * final_intensity;
    
    // Combine with base texture
    let combined_color = mix(base_color.rgb, final_color, 0.8);
    
    // Calculate alpha with soft falloff
    let alpha = base_color.a * in.color.a * final_intensity;
    
    return vec4<f32>(combined_color, alpha);
}
