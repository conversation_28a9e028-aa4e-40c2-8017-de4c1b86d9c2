@stage(compute) @workgroup_size(8, 8, 1)
fn update(@builtin(global_invocation_id) invocation_id: vec3<u32>) {
    let R: vec2<f32> = uni.iResolution.xy;
    let y_inverted_location = vec2<i32>(i32(invocation_id.x), i32(R.y) - i32(invocation_id.y));
    let location = vec2<i32>(i32(invocation_id.x), i32(invocation_id.y));
    
	var o: vec4<f32>;
	var u = vec2<f32>(f32(location.x), f32(location.y) );

	var v: vec2<f32> = uni.iResolution.xy;
	u = 0.2 * (u + u - v) / v.y;
	var z: vec4<f32> = o = vec4<f32>(1., 2., 3., 0.);

	for (var a: f32 = 0.5;
var t: f32 = uni.iTime;
var i: f32 = 0.5;  = i + 1i < 19.; o = o + ((1. + cos(z + t)) / length((1. + i * dot(v, v)) * sin(1.5 * u / (0.5 - dot(u, u)) - 9. * u.yx + t)))) {	v = cos( = t + 1t - 7. * u * pow(a = a + (0.03), i)) - 5. * u, u = u + (stanh(40. * dot(u = u * (mat2x2<f32>(cos(i + 0.02 * t - vec4<f32>(0., 11., 33., 0.)))), u) * cos(100. * u.yx + t)) / 200. + 0.2 * a * u + cos(4. / exp(dot(o, o) / 100.) + t) / 300.);
	}

	o = 25.6 / (min(o, 13.) + 164. / o) - dot(u, u) / 250.;
} 

