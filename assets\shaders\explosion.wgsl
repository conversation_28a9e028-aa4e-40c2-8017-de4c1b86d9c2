// Explosion Particle Shader
// Creates dramatic explosion effects with fire and smoke

#import bevy_enoki::particle_vertex_out::{ VertexOutput }

@group(1) @binding(0) var texture: texture_2d<f32>;
@group(1) @binding(1) var texture_sampler: sampler;

struct ExplosionMaterial {
    explosion_time: f32,
    intensity: f32,
}

@group(1) @binding(2) var<uniform> material: ExplosionMaterial;

// Hash function for random values
fn hash(p: vec2<f32>) -> f32 {
    let h = dot(p, vec2<f32>(127.1, 311.7));
    return fract(sin(h) * 43758.5453123);
}

// Smooth noise
fn noise(p: vec2<f32>) -> f32 {
    let i = floor(p);
    let f = fract(p);
    
    let a = hash(i);
    let b = hash(i + vec2<f32>(1.0, 0.0));
    let c = hash(i + vec2<f32>(0.0, 1.0));
    let d = hash(i + vec2<f32>(1.0, 1.0));
    
    let u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}

// Fractal Brownian Motion
fn fbm(p: vec2<f32>) -> f32 {
    var value = 0.0;
    var amplitude = 0.5;
    var frequency = 1.0;
    
    for (var i = 0; i < 5; i++) {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

@fragment
fn fragment(in: VertexOutput) -> @location(0) vec4<f32> {
    let uv = in.uv;
    let time = material.explosion_time + in.lifetime_frac * 2.0;
    let intensity = material.intensity;
    
    // Sample base texture
    let base_color = textureSample(texture, texture_sampler, uv);
    
    // Create turbulent motion for fire/smoke
    let turbulence = fbm(uv * 4.0 + vec2<f32>(time * 0.5, time * 0.8));
    let distorted_uv = uv + (turbulence - 0.5) * 0.2;
    
    // Distance from center for radial effects
    let center_dist = length(uv - vec2<f32>(0.5, 0.5));
    
    // Create explosion phases
    let explosion_phase = in.lifetime_frac;
    
    // Initial flash phase (0.0 - 0.1)
    let flash_intensity = smoothstep(0.1, 0.0, explosion_phase);
    
    // Fire phase (0.0 - 0.6)
    let fire_intensity = smoothstep(0.6, 0.0, explosion_phase) * (1.0 - flash_intensity);
    
    // Smoke phase (0.4 - 1.0)
    let smoke_intensity = smoothstep(0.4, 1.0, explosion_phase);
    
    // Color mixing based on phases
    var final_color = vec3<f32>(0.0);
    
    // Flash: bright white/yellow
    if (flash_intensity > 0.0) {
        final_color += vec3<f32>(1.0, 1.0, 0.8) * flash_intensity;
    }
    
    // Fire: orange to red gradient
    if (fire_intensity > 0.0) {
        let fire_gradient = 1.0 - center_dist * 2.0;
        let fire_color = mix(
            vec3<f32>(1.0, 0.3, 0.1), // Deep red
            vec3<f32>(1.0, 0.8, 0.2), // Bright orange
            fire_gradient * turbulence
        );
        final_color += fire_color * fire_intensity;
    }
    
    // Smoke: dark gray with some brown
    if (smoke_intensity > 0.0) {
        let smoke_color = mix(
            vec3<f32>(0.1, 0.1, 0.1), // Dark smoke
            vec3<f32>(0.3, 0.2, 0.1), // Brown smoke
            turbulence
        );
        final_color = mix(final_color, smoke_color, smoke_intensity * 0.7);
    }
    
    // Add some sparkle effects for debris
    let sparkle = step(0.95, hash(uv * 100.0 + time));
    final_color += vec3<f32>(1.0, 0.9, 0.6) * sparkle * fire_intensity;
    
    // Combine with base texture
    final_color = mix(base_color.rgb, final_color, 0.8);
    
    // Calculate alpha with proper falloff
    let edge_fade = 1.0 - smoothstep(0.3, 0.5, center_dist);
    let lifetime_fade = 1.0 - smoothstep(0.8, 1.0, explosion_phase);
    let alpha = base_color.a * in.color.a * edge_fade * lifetime_fade * intensity;
    
    return vec4<f32>(final_color, alpha);
}
