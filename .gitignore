# Rust/Cargo build artifacts
/target/
**/target/
Cargo.lock  # Include for binaries, exclude for libraries - this is a binary project

# Bevy-specific files
*.log
logs/
cache/
temp/
tmp/
.bevy_cache/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.vim/
.netrwhist
*.tmp
*.temp

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini
$RECYCLE.BIN/

# Windows
*.lnk

# macOS
.AppleDouble
.LSOverride
Icon

# Linux
*~

# Backup files
*.bak
*.backup
*.old
*.orig

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Executables
*.exe
*.out
*.app

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Profiling data
*.prof
perf.data*

# Test coverage
tarpaulin-report.html
lcov.info
coverage/

# Documentation build
book/
target/doc/

# Environment variables
.env
.env.local
.env.*.local

# Package manager files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Asset processing temporary files
*.tmp.png
*.tmp.jpg
*.tmp.jpeg
*.tmp.gif
*.tmp.bmp
*.tmp.tiff
*.tmp.webp
*.processing

# Game development specific
saves/
screenshots/
recordings/
replays/
user_data/
player_prefs/

# Shader compilation cache
*.spv
*.spirv
shader_cache/

# Audio processing temporary files
*.tmp.wav
*.tmp.ogg
*.tmp.mp3
*.tmp.flac

# 3D model temporary files
*.tmp.gltf
*.tmp.glb
*.tmp.obj
*.tmp.fbx
*.tmp.dae

# Texture processing
*.tmp.ktx2
*.tmp.basis

# Build system artifacts
.cargo/
rust-project.json

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]
Session.vim
.netrwhist

# Local configuration files
config.local.*
settings.local.*
*.local.toml
*.local.json
*.local.yaml
*.local.yml

# Performance profiling
flamegraph.svg
perf.data
callgrind.out.*

# Memory profiling
massif.out.*
heaptrack.*

# Benchmark results
criterion/
bench_results/

# Temporary prototype artifacts
prototype_temp/
experimental/
scratch/

lib_reference/
