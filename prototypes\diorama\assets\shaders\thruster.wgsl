#import bevy_pbr::mesh_vertex_output::MeshVertexOutput

struct ThrusterMaterial {
    power: f32,
    time: f32,
    flicker_speed: f32,
    base_color: vec4<f32>,
    size: f32,
}

@group(2) @binding(0) var<uniform> material: ThrusterMaterial;

@fragment
fn fragment(mesh: MeshVertexOutput) -> @location(0) vec4<f32> {
    // Get UV coordinates (0.0 to 1.0)
    let uv = mesh.uv;
    
    // Center the coordinates (-1.0 to 1.0)
    let centered_uv = (uv - 0.5) * 2.0;
    
    // Calculate distance from center
    let distance = length(centered_uv);
    
    // Create time-based animation
    let animated_time = material.time * material.flicker_speed;
    
    // Create multiple noise layers for realistic flame effect
    let noise1 = sin(distance * 8.0 + animated_time) * 0.5 + 0.5;
    let noise2 = sin(distance * 12.0 + animated_time * 1.3) * 0.5 + 0.5;
    let noise3 = sin(distance * 16.0 + animated_time * 0.7) * 0.5 + 0.5;
    
    // Combine noise layers
    let combined_noise = (noise1 + noise2 * 0.5 + noise3 * 0.3) / 1.8;
    
    // Create flame shape - stronger in center, fading outward
    let flame_core = 1.0 - smoothstep(0.0, 0.3, distance);
    let flame_outer = 1.0 - smoothstep(0.3, 0.8, distance);
    
    // Apply noise to flame intensity
    let flame_intensity = (flame_core * 1.5 + flame_outer * 0.5) * combined_noise;
    
    // Power affects overall intensity
    let powered_intensity = flame_intensity * material.power;
    
    // Create color gradient from center to edge
    let core_color = material.base_color.rgb;
    let edge_color = vec3<f32>(1.0, 0.8, 0.4); // Hot white-yellow edge
    let outer_color = vec3<f32>(0.8, 0.2, 0.0); // Red outer glow
    
    // Mix colors based on distance and intensity
    var final_color: vec3<f32>;
    if distance < 0.3 {
        // Core region - mix base color with hot edge
        let mix_factor = distance / 0.3;
        final_color = mix(core_color, edge_color, mix_factor);
    } else {
        // Outer region - mix edge with outer glow
        let mix_factor = (distance - 0.3) / 0.5;
        final_color = mix(edge_color, outer_color, mix_factor);
    }
    
    // Apply intensity and add some brightness boost
    final_color = final_color * powered_intensity * 2.0;
    
    // Create alpha falloff for smooth edges
    let alpha = powered_intensity * (1.0 - smoothstep(0.6, 1.0, distance));
    
    // Add some flickering to alpha for more dynamic effect
    let alpha_flicker = sin(animated_time * 2.0) * 0.1 + 0.9;
    let final_alpha = alpha * alpha_flicker * material.power;
    
    return vec4<f32>(final_color, final_alpha);
}
