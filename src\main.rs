use bevy::prelude::*;
use bevy::render::camera::ClearColorConfig;
use bevy_hanabi::prelude::*;

mod components;
use components::*;

fn main() {
    App::new()
        .add_plugins((
            DefaultPlugins,
            HanabiPlugin,
            PlayerPlugin,
            ParticleSystemPlugin,
        ))
        .add_systems(Startup, setup)
        .run();
}

fn setup(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    asset_server: Res<AssetServer>,
    particle_effects: Res<ParticleEffects>,
) {
    // Configure 3D camera for both ship and particles (bevy_hanabi supports 3D cameras)
    commands.spawn((
        Camera3d::default(),
        Transform::from_xyz(0.0, 5.0, 5.0).looking_at(Vec3::ZERO, Vec3::Y),
        Camera {
            clear_color: ClearColorConfig::Custom(Color::srgb(0.1, 0.1, 0.2)),
            ..default()
        },
    ));

    // Configure ambient lighting for good scene illumination
    commands.insert_resource(AmbientLight {
        color: Color::srgb(0.8, 0.8, 1.0),
        brightness: 0.3,
        affects_lightmapped_meshes: false,
    });

    // Add a static directional light for consistent illumination
    commands.spawn((
        DirectionalLight {
            color: Color::srgb(1.0, 0.95, 0.8),
            illuminance: 5000.0,
            shadows_enabled: false,
            affects_lightmapped_mesh_diffuse: true,
            shadow_depth_bias: 0.02,
            shadow_normal_bias: 0.6,
        },
        Transform::from_rotation(Quat::from_euler(EulerRot::XYZ, -0.5, 0.3, 0.0)),
    ));

    // Add a simple reference cube to ensure something is visible
    commands.spawn((
        Mesh3d(meshes.add(Cuboid::new(1.0, 1.0, 1.0))),
        MeshMaterial3d(materials.add(Color::srgb(1.0, 0.0, 0.0))),
        Transform::from_xyz(2.0, 0.5, 0.0),
    ));

    // Add a ground plane for reference
    commands.spawn((
        Mesh3d(meshes.add(Plane3d::default().mesh().size(10.0, 10.0))),
        MeshMaterial3d(materials.add(Color::srgb(0.3, 0.5, 0.3))),
    ));

    // Spawn the Player sprite at scene center, slightly elevated above ground plane
    // Player controls: WASD/Arrow keys for movement, Q/E for rotation
    // Player sprite will be properly lit by the orbiting light, demonstrating normal mapping effects

    // Create Player sprite directly for immediate visibility
    let ship_texture = asset_server.load("textures/ship.png");
    let ship_normal_map = asset_server.load("textures/ship_normal.png");
    let quad_mesh = meshes.add(Rectangle::new(2.0, 2.0));

    let player_material = materials.add(StandardMaterial {
        base_color_texture: Some(ship_texture),
        normal_map_texture: Some(ship_normal_map),
        metallic: 0.75,
        perceptual_roughness: 0.3,
        reflectance: 0.5,
        emissive: LinearRgba::rgb(0.0, 0.0, 0.0),
        base_color: Color::WHITE, // Remove green tint to see actual texture
        alpha_mode: AlphaMode::Blend, // Enable alpha blending for transparency
        ..default()
    });

    commands.spawn((
        create_player(Vec3::new(0.0, 0.1, 0.0)),
        Mesh3d(quad_mesh),
        MeshMaterial3d(player_material),
        PlayerSprite,
        EngineExhaust::default(),
    ));

    // Add multiple starfield background effects for better visibility
    create_starfield_effect(&mut commands, &particle_effects, Vec3::new(0.0, 0.0, -5.0));
    create_starfield_effect(&mut commands, &particle_effects, Vec3::new(50.0, 50.0, -5.0));
    create_starfield_effect(&mut commands, &particle_effects, Vec3::new(-50.0, 50.0, -5.0));
    create_starfield_effect(&mut commands, &particle_effects, Vec3::new(50.0, -50.0, -5.0));
    create_starfield_effect(&mut commands, &particle_effects, Vec3::new(-50.0, -50.0, -5.0));
}
