//! Point Light Orb System
//! 
//! Extracted from the lighting diorama prototype, this module provides
//! a synchronized point light + visible orb entity system with movement patterns.

use bevy::prelude::*;

/// Movement patterns for PointLightOrbs
#[derive(<PERSON><PERSON>, Debug)]
pub enum MovementPattern {
    /// No movement
    Static,
    /// Circular orbit around a center point
    Orbit { center: Vec3, radius: f32, speed: f32 },
    /// Figure-8 pattern
    Figure8 { center: Vec3, scale: f32, speed: f32 },
    /// Linear back-and-forth movement
    Linear { start: Vec3, end: Vec3, speed: f32 },
    /// Random floating movement
    Float { center: Vec3, range: f32, speed: f32 },
}

/// A synchronized point light + visible orb entity
/// This ensures the light and visual representation always stay in sync
#[derive(Component, Clone)]
pub struct PointLightOrb {
    /// Base color of the orb and light
    pub color: Color,
    /// Light intensity
    pub intensity: f32,
    /// Base intensity (for animations/flickering)
    pub base_intensity: f32,
    /// Orb radius
    pub radius: f32,
    /// Whether the orb should flicker/animate
    pub animate: bool,
    /// Animation speed multiplier
    pub animation_speed: f32,
    /// Light range
    pub range: f32,
    /// Movement pattern
    pub movement: MovementPattern,
    /// Internal timer for movement calculations
    pub movement_timer: f32,
}

impl Default for PointLightOrb {
    fn default() -> Self {
        Self {
            color: Color::WHITE,
            intensity: 800.0,
            base_intensity: 800.0,
            radius: 0.08,
            animate: true,
            animation_speed: 1.0,
            range: 20.0,
            movement: MovementPattern::Static,
            movement_timer: 0.0,
        }
    }
}

/// Marker component for the visual orb representation
#[derive(Component)]
pub struct OrbVisual;

/// Marker component for the light component of the orb
#[derive(Component)]
pub struct OrbLight;

/// Helper function to create a PointLightOrb with movement pattern
pub fn create_moving_point_light_orb(
    color: Color,
    intensity: f32,
    radius: f32,
    position: Vec3,
    animate: bool,
    movement: MovementPattern,
) -> impl Bundle {
    (
        PointLightOrb {
            color,
            intensity,
            base_intensity: intensity,
            radius,
            animate,
            movement,
            ..default()
        },
        Transform::from_translation(position),
        Visibility::default(),
    )
}

/// Plugin for the PointLightOrb system
pub struct PointLightOrbPlugin;

impl Plugin for PointLightOrbPlugin {
    fn build(&self, app: &mut App) {
        app.add_systems(
            Update,
            (
                spawn_point_light_orb_components,
                update_point_light_orb_movement,
                update_point_light_orb_animation,
                sync_orb_components,
            ),
        );
    }
}

/// System to spawn the visual and light components for PointLightOrbs
pub fn spawn_point_light_orb_components(
    mut commands: Commands,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    orb_query: Query<(Entity, &PointLightOrb, &Transform), (Added<PointLightOrb>, Without<OrbVisual>)>,
) {
    for (entity, orb, transform) in orb_query.iter() {
        // Create the visual orb (soft, glowing sphere)
        let orb_visual = commands.spawn((
            Mesh3d(meshes.add(Sphere::new(orb.radius))),
            MeshMaterial3d(materials.add(StandardMaterial {
                base_color: Color::LinearRgba(LinearRgba::from(orb.color).with_alpha(0.7)),
                emissive: LinearRgba::from(orb.color) * (orb.intensity / 1000.0),
                unlit: true,
                ..default()
            })),
            *transform,
            OrbVisual,
        )).id();

        // Create the point light
        let orb_light = commands.spawn((
            PointLight {
                color: orb.color,
                intensity: orb.intensity,
                range: orb.range,
                shadows_enabled: true,
                ..default()
            },
            *transform,
            OrbLight,
        )).id();

        // Add the child entities to the main orb entity
        commands.entity(entity).add_children(&[orb_visual, orb_light]);
    }
}

/// System to update PointLightOrb movement based on their movement patterns
pub fn update_point_light_orb_movement(
    time: Res<Time>,
    mut orb_query: Query<(&mut PointLightOrb, &mut Transform)>,
) {
    for (mut orb, mut transform) in orb_query.iter_mut() {
        orb.movement_timer += time.delta_secs();

        let new_position = match &orb.movement {
            MovementPattern::Static => transform.translation,
            MovementPattern::Orbit { center, radius, speed } => {
                let angle = orb.movement_timer * speed;
                *center + Vec3::new(
                    radius * angle.cos(),
                    0.0,
                    radius * angle.sin(),
                )
            }
            MovementPattern::Figure8 { center, scale, speed } => {
                let t = orb.movement_timer * speed;
                *center + Vec3::new(
                    scale * (2.0 * t).sin(),
                    0.0,
                    scale * t.sin(),
                )
            }
            MovementPattern::Linear { start, end, speed } => {
                let t = ((orb.movement_timer * speed).sin() + 1.0) / 2.0;
                start.lerp(*end, t)
            }
            MovementPattern::Float { center, range, speed } => {
                let t = orb.movement_timer * speed;
                *center + Vec3::new(
                    range * (t * 0.7).sin() * 0.5,
                    range * (t * 1.3).sin() * 0.3,
                    range * (t * 0.9).sin() * 0.5,
                )
            }
        };

        transform.translation = new_position;
    }
}

/// System to update PointLightOrb animation (flickering, pulsing)
pub fn update_point_light_orb_animation(
    time: Res<Time>,
    mut orb_query: Query<&mut PointLightOrb>,
) {
    for mut orb in orb_query.iter_mut() {
        if orb.animate {
            let time_factor = time.elapsed_secs() * orb.animation_speed;
            let flicker = 0.8 + 0.2 * (time_factor * 3.0).sin();
            orb.intensity = orb.base_intensity * flicker;
        }
    }
}

/// System to sync the orb's properties with its visual and light components
pub fn sync_orb_components(
    orb_query: Query<(&PointLightOrb, &Transform, &Children), Changed<PointLightOrb>>,
    mut visual_query: Query<&mut MeshMaterial3d<StandardMaterial>, (With<OrbVisual>, Without<OrbLight>)>,
    mut light_query: Query<&mut PointLight, (With<OrbLight>, Without<OrbVisual>)>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for (orb, _transform, children) in orb_query.iter() {
        for child in children.iter() {
            // Update visual component
            if let Ok(mut material_handle) = visual_query.get_mut(child) {
                if let Some(material) = materials.get_mut(&material_handle.0) {
                    material.base_color = Color::LinearRgba(LinearRgba::from(orb.color).with_alpha(0.7));
                    material.emissive = LinearRgba::from(orb.color) * (orb.intensity / 1000.0);
                }
            }

            // Update light component
            if let Ok(mut light) = light_query.get_mut(child) {
                light.color = orb.color;
                light.intensity = orb.intensity;
                light.range = orb.range;
            }
        }
    }
}
