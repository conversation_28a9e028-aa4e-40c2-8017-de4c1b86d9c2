# GitFlow Workflow Guide

This document outlines the GitFlow branching strategy used in the Fleeting project for organized, parallel development.

## 🌿 Branch Structure

### Main Branches

- **`main`**: Production-ready releases only

  - Contains stable, deployable code
  - Protected branch - no direct commits
  - Only accepts merges from release and hotfix branches
  - Each merge should be tagged with a version number

- **`develop`**: Integration branch for ongoing development
  - Contains the latest development changes
  - Base branch for all feature branches
  - Continuously integrated and tested
  - Merged to release branches for preparation

### Supporting Branches

#### Feature Branches

- **Naming**: `feature/descriptive-name`
- **Examples**: `feature/player-movement`, `feature/lighting-system`, `feature/audio-manager`
- **Purpose**: Develop new features or enhancements
- **Lifetime**: Created from `develop`, merged back to `develop`

#### Release Branches

- **Naming**: `release/v{major}.{minor}.{patch}`
- **Examples**: `release/v0.1.0`, `release/v1.2.3`
- **Purpose**: Prepare releases, bug fixes, version bumps
- **Lifetime**: Created from `develop`, merged to both `main` and `develop`

#### Hotfix Branches

- **Naming**: `hotfix/issue-description`
- **Examples**: `hotfix/crash-on-startup`, `hotfix/memory-leak`
- **Purpose**: Critical fixes for production issues
- **Lifetime**: Created from `main`, merged to both `main` and `develop`

## 🚀 Common Workflows

### Starting a New Feature

```bash
# Ensure you're on the latest develop
git checkout develop
git pull origin develop

# Create and switch to feature branch
git checkout -b feature/my-new-feature

# Work on your feature...
git add .
git commit -m "feat: implement basic player movement"

# Push feature branch
git push -u origin feature/my-new-feature
```

### Finishing a Feature

```bash
# Ensure develop is up to date
git checkout develop
git pull origin develop

# Merge feature branch
git merge --no-ff feature/my-new-feature

# Push updated develop
git push origin develop

# Clean up feature branch
git branch -d feature/my-new-feature
git push origin --delete feature/my-new-feature
```

### Creating a Release

```bash
# Create release branch from develop
git checkout develop
git pull origin develop
git checkout -b release/v0.1.0

# Prepare release (version bumps, changelog, etc.)
# ... make necessary changes ...
git commit -m "chore: prepare release v0.1.0"

# Merge to main
git checkout main
git pull origin main
git merge --no-ff release/v0.1.0

# Tag the release
git tag -a v0.1.0 -m "Release version 0.1.0"

# Merge back to develop
git checkout develop
git merge --no-ff release/v0.1.0

# Push everything
git push origin main
git push origin develop
git push origin v0.1.0

# Clean up release branch
git branch -d release/v0.1.0
git push origin --delete release/v0.1.0
```

### Emergency Hotfix

```bash
# Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-bug-fix

# Fix the issue
# ... make necessary changes ...
git commit -m "fix: resolve critical startup crash"

# Merge to main
git checkout main
git merge --no-ff hotfix/critical-bug-fix

# Tag if necessary
git tag -a v0.1.1 -m "Hotfix version 0.1.1"

# Merge to develop
git checkout develop
git merge --no-ff hotfix/critical-bug-fix

# Push everything
git push origin main
git push origin develop
git push origin v0.1.1

# Clean up hotfix branch
git branch -d hotfix/critical-bug-fix
git push origin --delete hotfix/critical-bug-fix
```

## 📝 Commit Message Convention

We follow [Conventional Commits](https://www.conventionalcommits.org/) format:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Types

- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples

```bash
feat(player): add basic movement controls
fix(lighting): resolve shadow rendering issue
docs(readme): update installation instructions
refactor(systems): reorganize ECS system structure
```

## 🔒 Branch Protection

### Recommended Settings

#### Main Branch

- Require pull request reviews
- Require status checks to pass
- Require branches to be up to date
- Restrict pushes to administrators only

#### Develop Branch

- Require pull request reviews (optional for small teams)
- Require status checks to pass
- Allow force pushes (for rebasing)

## 🎯 Best Practices

1. **Keep features small**: Aim for features that can be completed in 1-2 weeks
2. **Regular integration**: Merge features to develop frequently
3. **Clean history**: Use `git rebase` to clean up feature branch history before merging
4. **Descriptive names**: Use clear, descriptive branch and commit names
5. **Test before merge**: Ensure all tests pass before merging
6. **Document changes**: Update documentation with significant changes

## 🔄 Integration with Prototypes

### Extracting Prototype Features

When a prototype demonstrates valuable functionality:

```bash
# Create feature branch for prototype extraction
git checkout develop
git checkout -b feature/prototype-lighting-system

# Extract and refactor prototype code
# ... copy and adapt code from prototypes/ ...

# Commit the extracted feature
git add .
git commit -m "feat(lighting): extract point light orb system from diorama prototype"

# Follow normal feature workflow
```

### Prototype Branch Naming

- `feature/prototype-{functionality}`: For extracting prototype features
- `experiment/{name}`: For experimental branches that may not be merged

## 🛠️ Tools and Automation

### Recommended Git Aliases

```bash
git config --global alias.flow-feature-start '!f() { git checkout develop && git pull origin develop && git checkout -b feature/$1; }; f'
git config --global alias.flow-feature-finish '!f() { git checkout develop && git pull origin develop && git merge --no-ff feature/$1 && git branch -d feature/$1; }; f'
git config --global alias.flow-release-start '!f() { git checkout develop && git pull origin develop && git checkout -b release/$1; }; f'
```

### Usage

```bash
git flow-feature-start player-movement
git flow-feature-finish player-movement
git flow-release-start v0.1.0
```
